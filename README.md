# Real McCoy Returns - Shopify Theme App Extension

A comprehensive customer-facing order tracking and returns management widget for Shopify stores.

## Features

- **Order Lookup**: Customers can find their orders using email + order number
- **Order Tracking**: Real-time fulfillment status and shipping information
- **Returns Management**: Self-service return requests with item selection
- **Return Labels**: Automated return shipping label generation
- **Mobile Responsive**: Optimized for all device sizes
- **Accessible**: WCAG 2.1 AA compliant with full keyboard navigation
- **Polaris Design**: Consistent with Shopify's design system

## Tech Stack

- **Backend**: Remix with TypeScript
- **Frontend**: Vanilla JavaScript + Liquid templates
- **UI Framework**: Shopify Polaris design system
- **APIs**: Shopify GraphQL Admin API
- **Testing**: Jest with Testing Library
- **Development**: Shopify CLI 3.0+

## Development Setup

### Prerequisites

- Node.js 20.0.0+
- Shopify CLI 3.83.0+
- Shopify Partner account
- Development store access

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd real-mcoy-returns
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your Shopify app credentials
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Deploy to development store:**
   ```bash
   npm run deploy
   ```

### Environment Variables

Required environment variables in `.env`:

```env
SHOPIFY_API_KEY=your_api_key_here
SHOPIFY_API_SECRET=your_api_secret_here
SCOPES=read_orders,write_orders,read_customers,write_customers,read_fulfillments,write_fulfillments,read_returns,write_returns
HOST=https://your-ngrok-url.ngrok.io
SESSION_SECRET=your_session_secret_key
```

## Usage

### Theme Integration

1. Install the app in your Shopify store
2. Go to Online Store > Themes > Customize
3. Add the "Returns & Tracking Widget" block to any section
4. Configure widget settings:
   - Widget title
   - Primary color
   - Enable/disable returns
   - Enable/disable tracking
   - Return window (days)

### Widget Configuration

The widget supports the following theme editor settings:

- **Widget Title**: Customizable heading text
- **Primary Color**: Brand color integration
- **Enable Returns**: Toggle return functionality
- **Enable Tracking**: Toggle order tracking
- **Return Window**: Days after delivery for returns (7-90)

## API Endpoints

### POST /api/orders/lookup
Lookup order by email and order number.

**Request:**
```json
{
  "email": "<EMAIL>",
  "orderNumber": "1001"
}
```

**Response:**
```json
{
  "success": true,
  "order": {
    "id": "gid://shopify/Order/123",
    "name": "#1001",
    "email": "<EMAIL>",
    "displayFulfillmentStatus": "fulfilled",
    "totalPriceSet": {
      "presentmentMoney": {
        "amount": "99.99",
        "currencyCode": "USD"
      }
    },
    "lineItems": { ... },
    "fulfillments": { ... }
  }
}
```

### POST /api/returns/create
Create return request for order items.

**Request:**
```json
{
  "orderId": "gid://shopify/Order/123",
  "items": [
    {
      "lineItemId": "gid://shopify/LineItem/456",
      "quantity": 1,
      "reason": "defective"
    }
  ],
  "reason": "defective",
  "comments": "Item arrived damaged"
}
```

**Response:**
```json
{
  "success": true,
  "return": {
    "id": "gid://shopify/Return/789",
    "status": "open",
    "returnLineItems": { ... }
  }
}
```

## Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Test Structure

```
tests/
├── unit/           # Unit tests for utilities and components
├── integration/    # API integration tests
└── setup.ts       # Test configuration and mocks
```

## Deployment

### Development Deployment
```bash
npm run deploy
```

### Production Deployment
1. Update environment variables for production
2. Run full test suite: `npm test`
3. Build for production: `npm run build`
4. Deploy via Shopify CLI: `npm run deploy`

## Project Structure

```
real-mcoy-returns/
├── app/                          # Remix backend
│   ├── routes/                   # API endpoints
│   │   ├── api.orders.lookup.ts  # Order lookup
│   │   └── api.returns.create.ts # Return creation
│   ├── lib/                      # Utilities
│   │   └── shopify-graphql.ts    # GraphQL client
│   └── shopify.server.ts         # Shopify configuration
├── extensions/
│   └── real-mcoy-returns-widget/
│       ├── blocks/
│       │   └── returns-widget.liquid    # Main widget block
│       ├── snippets/                    # Reusable components
│       │   ├── order-lookup.liquid
│       │   ├── order-display.liquid
│       │   ├── tracking-info.liquid
│       │   ├── return-form.liquid
│       │   └── return-confirmation.liquid
│       ├── assets/
│       │   ├── widget.js                # Widget JavaScript
│       │   └── widget.css               # Widget styles
│       ├── locales/
│       │   └── en.default.json          # Translations
│       └── shopify.extension.toml       # Extension config
├── tests/                               # Test files
└── docs/                               # Documentation
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Accessibility Features

- WCAG 2.1 AA compliant
- Full keyboard navigation
- Screen reader support
- High contrast mode support
- Reduced motion support
- Semantic HTML structure
- ARIA labels and roles

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in this repository
- Contact the development team
- Check the Shopify Partner documentation

## Changelog

### v1.0.0
- Initial release
- Order lookup functionality
- Order tracking display
- Return request system
- Mobile responsive design
- Accessibility compliance