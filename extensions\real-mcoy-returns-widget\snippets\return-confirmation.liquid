<div class="return-confirmation-container">
  <div class="confirmation-header">
    <div class="success-icon">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M22 11.08V12a10 10 0 11-5.93-9.14"/>
        <path d="M22 4L12 14.01l-3-3"/>
      </svg>
    </div>
    <h3>Return Request Submitted</h3>
    <p class="confirmation-message">Your return request has been successfully submitted and is being processed.</p>
  </div>

  <div class="return-details" id="return-confirmation-details">
    <!-- Return details will be populated by JavaScript -->
  </div>

  <div class="next-steps">
    <h4>What happens next?</h4>
    <div class="steps-list">
      <div class="step">
        <div class="step-number">1</div>
        <div class="step-content">
          <h5>Processing</h5>
          <p>We'll review your return request within 1-2 business days.</p>
        </div>
      </div>
      <div class="step">
        <div class="step-number">2</div>
        <div class="step-content">
          <h5>Return Label</h5>
          <p>Once approved, you'll receive a prepaid return shipping label via email.</p>
        </div>
      </div>
      <div class="step">
        <div class="step-number">3</div>
        <div class="step-content">
          <h5>Ship Items</h5>
          <p>Package your items securely and drop them off at any authorized shipping location.</p>
        </div>
      </div>
      <div class="step">
        <div class="step-number">4</div>
        <div class="step-content">
          <h5>Refund</h5>
          <p>Your refund will be processed within 5-7 business days after we receive your items.</p>
        </div>
      </div>
    </div>
  </div>

  <div class="confirmation-actions">
    <button type="button" data-action="print-confirmation" class="btn btn-secondary">
      <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="6,9 6,2 18,2 18,9"/>
        <path d="M6 18H4a2 2 0 01-2-2v-5a2 2 0 012-2h16a2 2 0 012 2v5a2 2 0 01-2 2h-2"/>
        <rect x="6" y="14" width="12" height="8"/>
      </svg>
      Print Confirmation
    </button>
    
    <button type="button" data-action="new-lookup" class="btn btn-primary">
      <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
      </svg>
      Look Up Another Order
    </button>
  </div>
</div>