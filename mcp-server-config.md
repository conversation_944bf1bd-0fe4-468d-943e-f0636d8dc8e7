# MCP Server Configuration
## Shopify Development MCP Server Setup

### Overview
This document provides complete configuration for integrating Shopify's official MCP server with AI coding tools (<PERSON>, <PERSON>ursor) to access real-time Shopify documentation and API references while building the Real McCoy Returns app.

---

## Official Shopify Dev MCP Server

### What It Provides
The official Shopify Dev MCP server gives AI assistants access to:
- **Latest Shopify API Documentation** (GraphQL Admin API, REST API)
- **Polaris Web Components** documentation (unified design system)
- **Theme App Extensions** guides and best practices
- **Real-time API Schema** updates and changes
- **Code Examples** and implementation patterns

### Key Features
- 🔄 **Always Up-to-Date:** Pulls directly from shopify.dev
- 📚 **Comprehensive:** Covers all Shopify APIs and frameworks
- 🎨 **Polaris Integration:** Latest web components documentation
- 🔍 **Smart Search:** Contextual documentation retrieval
- 💡 **Code Generation:** API-aware code suggestions

---

## Claude Desktop Configuration

### Installation Steps

1. **Locate Configuration File:**
   ```bash
   # macOS
   ~/Library/Application Support/Claude/claude_desktop_config.json
   
   # Windows
   %APPDATA%/Claude/claude_desktop_config.json
   
   # Linux
   ~/.config/claude/claude_desktop_config.json
   ```

2. **Add Shopify MCP Server:**
   ```json
   {
     "mcpServers": {
       "shopify-dev-mcp": {
         "command": "npx",
         "args": ["-y", "@shopify/dev-mcp@latest"],
         "env": {
           "POLARIS_UNIFIED": "true",
           "OPT_OUT_INSTRUMENTATION": "false"
         }
       }
     }
   }
   ```

3. **Windows Alternative Configuration:**
   ```json
   {
     "mcpServers": {
       "shopify-dev-mcp": {
         "command": "cmd",
         "args": ["/k", "npx", "-y", "@shopify/dev-mcp@latest"],
         "env": {
           "POLARIS_UNIFIED": "true",
           "OPT_OUT_INSTRUMENTATION": "false"
         }
       }
     }
   }
   ```

4. **Restart Claude Desktop** for changes to take effect.

### Environment Variables

#### `POLARIS_UNIFIED="true"`
- Enables access to the new unified Polaris web components
- Includes documentation for Shopify's 2025 design system update
- Essential for theme app extension development

#### `OPT_OUT_INSTRUMENTATION="false"`
- Allows anonymous usage analytics to improve the MCP server
- Set to `"true"` if you prefer to disable analytics
- Does not affect functionality, only telemetry

---

## Cursor Configuration

### Installation Steps

1. **Open Cursor Settings:**
   - Mac: `Cmd + ,`
   - Windows/Linux: `Ctrl + ,`

2. **Navigate to Extensions → MCP**
   Or manually edit settings file at `.cursor-settings/settings.json`

3. **Add Configuration:**
   ```json
   {
     "mcpServers": {
       "shopify-dev-mcp": {
         "command": "npx",
         "args": ["-y", "@shopify/dev-mcp@latest"],
         "env": {
           "POLARIS_UNIFIED": "true",
           "OPT_OUT_INSTRUMENTATION": "false"
         }
       }
     }
   }
   ```

4. **Restart Cursor** to activate the MCP server.

---

## Verification & Testing

### Verify Installation

1. **Check Server Status:**
   In Claude Desktop or Cursor, try asking:
   ```
   Can you access Shopify's GraphQL Admin API documentation?
   Show me the latest theme app extension configuration format.
   What are the new Polaris web components available?
   ```

2. **Test Specific Queries:**
   ```
   How do I query orders using GraphQL Admin API with email filtering?
   What's the current schema for theme app extension blocks?
   Show me Polaris button component documentation.
   ```

3. **Verify Polaris Access:**
   ```
   What are the new unified Polaris components for 2025?
   How do I use Polaris web components in theme extensions?
   ```

### Troubleshooting

#### Server Not Connecting
```bash
# Test the MCP server directly
npx @shopify/dev-mcp@latest

# If this fails, check Node.js version
node --version  # Should be 18.0.0+

# Update npm and try again
npm install -g npm@latest
```

#### Configuration Issues
1. **Check JSON Syntax:** Ensure valid JSON in config file
2. **Verify Paths:** Confirm config file location is correct
3. **Restart Application:** Always restart after config changes
4. **Check Logs:** Look for error messages in app console

#### Network Issues
- Ensure internet connection for initial download
- Check if corporate firewall blocks npm registry
- Try running `npx @shopify/dev-mcp@latest` manually first

---

## Advanced Configuration

### Multiple MCP Servers
You can run multiple MCP servers simultaneously:

```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx",
      "args": ["-y", "@shopify/dev-mcp@latest"],
      "env": {
        "POLARIS_UNIFIED": "true"
      }
    },
    "local-shopify-mcp": {
      "command": "node",
      "args": ["/path/to/local/shopify-mcp-server.js"],
      "env": {
        "SHOPIFY_ACCESS_TOKEN": "your_token_here",
        "MYSHOPIFY_DOMAIN": "returns-testing-store.myshopify.com"
      }
    }
  }
}
```

### Custom Environment Settings

```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx",
      "args": ["-y", "@shopify/dev-mcp@latest"],
      "env": {
        "POLARIS_UNIFIED": "true",
        "OPT_OUT_INSTRUMENTATION": "true",
        "NODE_ENV": "development",
        "DEBUG": "shopify:*"
      }
    }
  }
}
```

---

## Available Tools & Commands

Once configured, the MCP server provides these tools to AI assistants:

### Documentation Tools
- **`search_docs`** - Search across all Shopify documentation
- **`get_api_instructions`** - Get specific API implementation guidance
- **`fetch_docs_by_path`** - Retrieve complete documentation pages

### API Reference Tools
- **`graphql_schema`** - Access current GraphQL Admin API schema
- **`rest_api_reference`** - REST API endpoint documentation
- **`webhook_reference`** - Webhook event documentation

### Component Documentation
- **`polaris_components`** - Web component documentation
- **`theme_extension_guides`** - Theme app extension patterns
- **`app_bridge_reference`** - App Bridge API documentation

---

## Integration with Real McCoy Returns Development

### Optimized Prompts

When working on the Real McCoy Returns app, use these enhanced prompts:

```
Using the latest Shopify GraphQL Admin API documentation, show me how to:
1. Query orders with email and order number filtering
2. Create returns with line item selection
3. Generate return labels with proper formatting

Please include current API version and any recent changes.
```

```
Reference the current Polaris web components documentation to create:
1. A form layout for order lookup
2. Status badges for order fulfillment
3. A button group for actions

Use the unified Polaris design system from 2025.
```

### Context-Aware Development

The MCP server enables AI assistants to:
- **Stay Current:** Always reference latest API versions
- **Avoid Deprecated Features:** Skip outdated patterns automatically
- **Follow Best Practices:** Use current Shopify recommendations
- **Generate Accurate Code:** Based on real schema definitions

---

## Benefits for Real McCoy Returns

### Development Speed
- **Instant Documentation:** No need to manually search shopify.dev
- **Current Examples:** Always up-to-date code patterns
- **API Accuracy:** Correct field names and types

### Code Quality
- **Best Practices:** Follow Shopify's latest recommendations
- **Error Prevention:** Avoid deprecated or incorrect API usage
- **Consistency:** Maintain patterns across the codebase

### Maintenance
- **Future-Proof:** Automatically get updates as APIs evolve
- **Migration Guidance:** Receive guidance on API changes
- **Compatibility:** Ensure compatibility with latest Shopify features

---

## Security & Privacy

### Data Handling
- **No Sensitive Data:** MCP server only accesses public documentation
- **No API Keys:** Documentation access doesn't require authentication
- **Local Processing:** All requests processed locally

### Analytics (Optional)
- **Anonymous Usage:** Only aggregated usage statistics if enabled
- **No Code Analysis:** Your code is never sent to Shopify
- **Opt-Out Available:** Set `OPT_OUT_INSTRUMENTATION="true"`

---

## Support & Updates

### Staying Updated
The MCP server auto-updates with each use via `@latest` tag:
- Documentation stays current with shopify.dev
- New features and APIs automatically available
- Breaking changes communicated through AI assistant

### Getting Help
- **Official Repo:** https://github.com/Shopify/dev-mcp
- **Issues:** Report problems on GitHub
- **Community:** Shopify Partner Discord and forums

### Version Pinning (Advanced)
For production environments, you can pin to specific versions:
```json
{
  "command": "npx",
  "args": ["-y", "@shopify/dev-mcp@1.1.0"]
}
```

This MCP server configuration provides AI coding assistants with comprehensive, real-time access to Shopify's development resources, significantly improving development speed and code quality for the Real McCoy Returns project.