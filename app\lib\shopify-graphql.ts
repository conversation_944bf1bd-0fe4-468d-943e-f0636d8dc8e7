import { GraphqlQueryError } from "@shopify/shopify-api";

export interface OrderLookupParams {
  email: string;
  orderNumber: string;
}

export interface OrderData {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  displayFulfillmentStatus: string;
  totalPriceSet: {
    presentmentMoney: {
      amount: string;
      currencyCode: string;
    };
  };
  lineItems: {
    edges: Array<{
      node: {
        id: string;
        title: string;
        quantity: number;
        variant?: {
          id: string;
          image?: {
            url: string;
          };
        };
        originalTotalSet: {
          presentmentMoney: {
            amount: string;
            currencyCode: string;
          };
        };
      };
    }>;
  };
  fulfillments: {
    edges: Array<{
      node: {
        id: string;
        status: string;
        trackingCompany?: string;
        trackingNumber?: string;
        trackingUrl?: string;
      };
    }>;
  };
  shippingAddress?: {
    firstName?: string;
    lastName?: string;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    country?: string;
    zip?: string;
  };
}

export interface ReturnItem {
  lineItemId: string;
  quantity: number;
  reason: string;
}

export interface ReturnData {
  orderId: string;
  items: ReturnItem[];
  reason: string;
  comments?: string;
}

const ORDER_LOOKUP_QUERY = `
  query orderLookup($query: String!) {
    orders(first: 1, query: $query) {
      edges {
        node {
          id
          name
          email
          createdAt
          displayFulfillmentStatus
          totalPriceSet {
            presentmentMoney {
              amount
              currencyCode
            }
          }
          lineItems(first: 50) {
            edges {
              node {
                id
                title 
                quantity
                variant {
                  id
                  image {
                    url
                  }
                }
                originalTotalSet {
                  presentmentMoney {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
          fulfillments(first: 10) {
            edges {
              node {
                id
                status
                trackingCompany
                trackingNumber
                trackingUrl
              }
            }
          }
          shippingAddress {
            firstName
            lastName
            address1
            address2
            city
            province
            country
            zip
          }
        }
      }
    }
  }
`;

const RETURN_CREATE_MUTATION = `
  mutation returnCreate($input: ReturnInput!) {
    returnCreate(input: $input) {
      return {
        id
        status
        returnLineItems(first: 50) {
          edges {
            node {
              id
              quantity
              returnReason
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export async function lookupOrder(
  graphql: any,
  { email, orderNumber }: OrderLookupParams
): Promise<{ success: boolean; order?: OrderData; error?: string }> {
  try {
    // Clean order number (remove # if present)
    const cleanOrderNumber = orderNumber.replace(/^#/, '');
    
    // Build search query - search by order name and email
    const searchQuery = `name:${cleanOrderNumber} AND email:${email}`;
    
    const response = await graphql(ORDER_LOOKUP_QUERY, {
      variables: { query: searchQuery }
    });

    const orders = response.data?.orders?.edges || [];
    
    if (orders.length === 0) {
      return {
        success: false,
        error: "Order not found. Please check your email address and order number."
      };
    }

    const order = orders[0].node;
    
    // Verify email matches (case insensitive)
    if (order.email?.toLowerCase() !== email.toLowerCase()) {
      return {
        success: false,
        error: "Order not found. Please check your email address and order number."
      };
    }

    return {
      success: true,
      order: order as OrderData
    };

  } catch (error) {
    console.error('Order lookup error:', error);
    
    if (error instanceof GraphqlQueryError) {
      return {
        success: false,
        error: "Unable to lookup order. Please try again."
      };
    }
    
    return {
      success: false,
      error: "An unexpected error occurred. Please try again."
    };
  }
}

export async function createReturn(
  graphql: any,
  returnData: ReturnData
): Promise<{ success: boolean; return?: any; error?: string }> {
  try {
    const returnLineItems = returnData.items.map(item => ({
      lineItemId: item.lineItemId,
      quantity: item.quantity,
      returnReason: item.reason
    }));

    const response = await graphql(RETURN_CREATE_MUTATION, {
      variables: {
        input: {
          orderId: returnData.orderId,
          returnLineItems,
          note: returnData.comments
        }
      }
    });

    const result = response.data?.returnCreate;
    
    if (result?.userErrors?.length > 0) {
      return {
        success: false,
        error: result.userErrors[0].message
      };
    }

    return {
      success: true,
      return: result?.return
    };

  } catch (error) {
    console.error('Return creation error:', error);
    return {
      success: false,
      error: "Unable to create return request. Please try again."
    };
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validateOrderNumber(orderNumber: string): boolean {
  const cleaned = orderNumber.replace(/^#/, '');
  return cleaned.length > 0 && /^\d+$/.test(cleaned);
}