import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createReturn } from "../lib/shopify-graphql";

export async function action({ request }: ActionFunctionArgs) {
  const { admin } = await authenticate.public.appProxy(request);
  
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { orderId, items, reason, comments } = body;

    // Validate input
    if (!orderId || !items || !Array.isArray(items) || items.length === 0) {
      return json({ 
        success: false, 
        error: "Order ID and items are required" 
      }, { status: 400 });
    }

    // Validate reason
    if (!reason) {
      return json({ 
        success: false, 
        error: "Return reason is required" 
      }, { status: 400 });
    }

    // Validate each item
    for (const item of items) {
      if (!item.lineItemId || !item.quantity || item.quantity <= 0) {
        return json({ 
          success: false, 
          error: "Invalid item data" 
        }, { status: 400 });
      }
    }

    const result = await createReturn(admin.graphql, {
      orderId,
      items,
      reason,
      comments
    });
    
    return json(result);

  } catch (error) {
    console.error("Return creation API error:", error);
    return json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
}