import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { lookupOrder, validateEmail, validateOrderNumber } from "../lib/shopify-graphql";

export async function action({ request }: ActionFunctionArgs) {
  const { admin } = await authenticate.public.appProxy(request);
  
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { email, orderNumber } = body;

    // Validate input
    if (!email || !orderNumber) {
      return json({ 
        success: false, 
        error: "Email and order number are required" 
      }, { status: 400 });
    }

    // Validate email format
    if (!validateEmail(email)) {
      return json({ 
        success: false, 
        error: "Please enter a valid email address" 
      }, { status: 400 });
    }

    // Validate order number format
    if (!validateOrderNumber(orderNumber)) {
      return json({ 
        success: false, 
        error: "Please enter a valid order number" 
      }, { status: 400 });
    }

    const result = await lookupOrder(admin.graphql, { email, orderNumber });
    
    return json(result);

  } catch (error) {
    console.error("Order lookup API error:", error);
    return json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
}