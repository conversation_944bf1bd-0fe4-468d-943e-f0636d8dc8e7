import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return json({
    appName: "Real McCoy Returns",
    description: "Customer-facing order tracking and returns management widget",
  });
};

export default function Index() {
  const { appName, description } = useLoaderData<typeof loader>();

  return (
    <div style={{ fontFamily: "system-ui, sans-serif", lineHeight: "1.8", padding: "2rem" }}>
      <h1>{appName}</h1>
      <p>{description}</p>
      
      <div style={{ marginTop: "2rem" }}>
        <h2>App Status</h2>
        <p>✅ App is installed and running</p>
        <p>✅ Theme extension deployed</p>
        <p>✅ API endpoints configured</p>
      </div>
      
      <div style={{ marginTop: "2rem" }}>
        <h2>Setup Instructions</h2>
        <ol>
          <li>Go to your store's theme editor</li>
          <li>Add the "Returns & Tracking Widget" block to any section</li>
          <li>Configure the widget settings as needed</li>
          <li>Save and publish your theme</li>
        </ol>
      </div>
    </div>
  );
}