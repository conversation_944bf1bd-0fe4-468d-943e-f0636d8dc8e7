# Technical Specification
## Real McCoy Returns - Shopify Theme App Extension

### Architecture Overview

#### System Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Shopify Storefront                       │
│  ┌─────────────────────────────────────────────────────┐   │
│  │         Theme App Extension (Widget)                │   │
│  │  ┌─────────────────┐  ┌─────────────────────────┐   │   │
│  │  │   Frontend      │  │    Liquid Templates     │   │   │
│  │  │  (JavaScript)   │  │   (Polaris Components)  │   │   │
│  │  └─────────────────┘  └─────────────────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP Requests
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Shopify App Backend                      │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              Remix Server                           │   │
│  │  ┌─────────────────┐  ┌─────────────────────────┐   │   │
│  │  │   API Routes    │  │   GraphQL Integration   │   │   │
│  │  │   (app/routes)  │  │   (Admin API Client)    │   │   │
│  │  └─────────────────┘  └─────────────────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ GraphQL Queries/Mutations
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 Shopify GraphQL Admin API                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │   Orders    │ │   Returns   │ │      Fulfillments       ││
│  │     API     │ │     API     │ │         API             ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### Project Structure

#### Directory Layout
```
real-mcoy-returns/
├── app/                          # Remix application
│   ├── routes/                   # API routes
│   │   ├── api.orders.lookup.ts  # Order lookup endpoint
│   │   ├── api.orders.$id.ts     # Order details endpoint  
│   │   ├── api.returns.create.ts # Return creation endpoint
│   │   └── api.returns.$id.label.ts # Return label generation
│   ├── lib/                      # Utility functions
│   │   ├── shopify-graphql.ts    # GraphQL client setup
│   │   ├── order-validation.ts   # Order lookup validation
│   │   ├── return-logic.ts       # Return business logic
│   │   └── label-generator.ts    # Return label creation
│   └── shopify.server.ts         # Shopify app configuration
├── extensions/
│   └── real-mcoy-returns-widget/ # Theme App Extension
│       ├── shopify.extension.toml # Extension configuration
│       ├── assets/               # CSS, JS, images
│       │   ├── widget.css        # Widget styling
│       │   ├── widget.js         # Widget JavaScript
│       │   └── icons/            # UI icons
│       ├── blocks/               # App blocks
│       │   └── returns-widget.liquid # Main widget block
│       ├── snippets/             # Reusable liquid snippets
│       │   ├── order-lookup.liquid
│       │   ├── order-display.liquid
│       │   ├── tracking-info.liquid
│       │   ├── return-form.liquid
│       │   └── return-label.liquid
│       └── locales/              # Internationalization
│           └── en.default.json   # English translations
├── shopify.app.toml              # App configuration
├── package.json                  # Dependencies
└── README.md                     # Setup instructions
```

### Frontend Implementation

#### Theme App Extension Configuration
**File:** `extensions/real-mcoy-returns-widget/shopify.extension.toml`
```toml
name = "real-mcoy-returns-widget"
type = "theme_app_extension"

[extensions]
  [[extensions.blocks]]
    name = "Returns & Tracking Widget"
    target = "section"
    
  [[extensions.app_embeds]]
    name = "Returns Widget Embed" 
    target = "head"
```

#### Main Widget Block Structure
**File:** `extensions/real-mcoy-returns-widget/blocks/returns-widget.liquid`
```liquid
{%- liquid
  # Configuration from theme editor
  assign widget_title = block.settings.widget_title | default: 'Order Tracking & Returns'
  assign primary_color = block.settings.primary_color | default: '#1a73e8'
  assign enable_returns = block.settings.enable_returns | default: true
  assign enable_tracking = block.settings.enable_tracking | default: true
-%}

#### API Route Implementation
**File:** `app/routes/api.orders.lookup.ts`
```typescript
import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { lookupOrder } from "../lib/shopify-graphql";

export async function action({ request }: ActionFunctionArgs) {
  const { admin } = await authenticate.public.appProxy(request);
  
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { email, orderNumber } = body;

    // Validate input
    if (!email || !orderNumber) {
      return json({ 
        success: false, 
        error: "Email and order number are required" 
      }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return json({ 
        success: false, 
        error: "Please enter a valid email address" 
      }, { status: 400 });
    }

    const result = await lookupOrder(admin.graphql, { email, orderNumber });
    
    return json(result);

  } catch (error) {
    console.error("Order lookup API error:", error);
    return json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
}
```

**File:** `app/routes/api.returns.create.ts`
```typescript
import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createReturn } from "../lib/shopify-graphql";

export async function action({ request }: ActionFunctionArgs) {
  const { admin } = await authenticate.public.appProxy(request);
  
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { orderId, items, reason, comments } = body;

    // Validate input
    if (!orderId || !items || !Array.isArray(items) || items.length === 0) {
      return json({ 
        success: false, 
        error: "Order ID and items are required" 
      }, { status: 400 });
    }

    // Validate each item
    for (const item of items) {
      if (!item.lineItemId || !item.quantity || item.quantity <= 0) {
        return json({ 
          success: false, 
          error: "Invalid item data" 
        }, { status: 400 });
      }
    }

    const result = await createReturn(admin.graphql, {
      orderId,
      items,
      reason,
      comments
    });
    
    return json(result);

  } catch (error) {
    console.error("Return creation API error:", error);
    return json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
}
```

### Liquid Template Components

#### Order Lookup Form
**File:** `extensions/real-mcoy-returns-widget/snippets/order-lookup.liquid`
```liquid
<div class="order-lookup-container">
  <form class="order-lookup-form" method="post">
    <div class="form-group">
      <label for="email">Email Address</label>
      <input 
        type="email" 
        id="email" 
        name="email" 
        required 
        placeholder="Enter your email address"
        class="form-input"
      >
    </div>
    
    <div class="form-group">
      <label for="order_number">Order Number</label>
      <input 
        type="text" 
        id="order_number" 
        name="order_number" 
        required 
        placeholder="e.g. #1001 or 1001"
        class="form-input"
      >
      <small class="help-text">
        You can find your order number in your email confirmation
      </small>
    </div>
    
    <button type="submit" class="btn btn-primary">
      Find My Order
    </button>
  </form>
</div>
```

#### Return Form Component
**File:** `extensions/real-mcoy-returns-widget/snippets/return-form.liquid`
```liquid
<div class="return-form-container">
  <h3>Request a Return</h3>
  
  <form class="return-form" method="post">
    <div class="returnable-items">
      <h4>Select Items to Return</h4>
      <div id="return-items-list">
        <!-- Populated by JavaScript -->
      </div>
    </div>
    
    <div class="form-group">
      <label for="return_reason">Reason for Return</label>
      <select id="return_reason" name="return_reason" required class="form-select">
        <option value="">Select a reason</option>
        <option value="defective">Defective/Damaged</option>
        <option value="wrong_size">Wrong Size</option>
        <option value="wrong_item">Wrong Item Received</option>
        <option value="not_as_described">Not as Described</option>
        <option value="changed_mind">Changed Mind</option>
        <option value="other">Other</option>
      </select>
    </div>
    
    <div class="form-group">
      <label for="comments">Additional Comments (Optional)</label>
      <textarea 
        id="comments" 
        name="comments" 
        rows="3" 
        placeholder="Please provide additional details about your return..."
        class="form-textarea"
      ></textarea>
    </div>
    
    <div class="form-actions">
      <button type="button" data-action="back" class="btn btn-secondary">
        ← Back
      </button>
      <button type="submit" class="btn btn-primary">
        Submit Return Request
      </button>
    </div>
  </form>
</div>
```

### CSS Styling (Polaris-inspired)

**File:** `extensions/real-mcoy-returns-widget/assets/widget.css`
```css
/* Real McCoy Returns Widget Styles */
.real-mcoy-returns-widget {
  max-width: 600px;
  margin: 0 auto;
  padding: 1.5rem;
  border: 1px solid var(--p-color-border-subdued);
  border-radius: 8px;
  background: var(--p-color-surface);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.widget-header h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--p-color-text);
}

.widget-step {
  transition: opacity 0.3s ease-in-out;
}

.widget-step.hidden {
  display: none;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--p-color-text);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--p-color-border);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--p-color-border-interactive);
  box-shadow: 0 0 0 2px var(--p-color-border-interactive);
}

.help-text {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--p-color-text-subdued);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--p-color-bg-interactive);
  color: var(--p-color-text-on-interactive);
  border-color: var(--p-color-bg-interactive);
}

.btn-primary:hover {
  background: var(--p-color-bg-interactive-hover);
  border-color: var(--p-color-bg-interactive-hover);
}

.btn-secondary {
  background: var(--p-color-surface);
  color: var(--p-color-text);
  border-color: var(--p-color-border);
}

.btn-secondary:hover {
  background: var(--p-color-surface-hovered);
}

.btn-link {
  background: transparent;
  color: var(--p-color-text-interactive);
  border: none;
  padding: 0.5rem;
}

.btn-link:hover {
  color: var(--p-color-text-interactive-hover);
  text-decoration: underline;
}

/* Order Display */
.order-summary {
  padding: 1.5rem;
  background: var(--p-color-surface-subdued);
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.order-summary h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--p-color-bg-success-subdued);
  color: var(--p-color-text-success);
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.order-items {
  margin-bottom: 1.5rem;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--p-color-border-subdued);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.order-item img {
  margin-right: 1rem;
  border-radius: 4px;
}

.item-details h5 {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.item-details p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: var(--p-color-text-subdued);
}

/* Loading States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--p-color-border-subdued);
  border-top: 3px solid var(--p-color-border-interactive);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Messages */
.error-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--p-color-bg-critical-subdued);
  color: var(--p-color-text-critical);
  border: 1px solid var(--p-color-border-critical);
  border-radius: 4px;
  margin-bottom: 1rem;
}

.error-banner button {
  background: none;
  border: none;
  color: var(--p-color-text-critical);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .real-mcoy-returns-widget {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .order-item {
    flex-direction: column;
    text-align: center;
  }
  
  .order-item img {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .form-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .btn {
    width: 100%;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .real-mcoy-returns-widget {
    --p-color-surface: #1a1a1a;
    --p-color-text: #ffffff;
    --p-color-border: #404040;
    --p-color-border-subdued: #2a2a2a;
    --p-color-text-subdued: #b3b3b3;
    --p-color-surface-subdued: #2a2a2a;
    --p-color-surface-hovered: #333333;
    --p-color-bg-interactive: #006eff;
    --p-color-text-on-interactive: #ffffff;
    --p-color-bg-interactive-hover: #0052cc;
    --p-color-border-interactive: #006eff;
    --p-color-text-interactive: #006eff;
    --p-color-text-interactive-hover: #0052cc;
  }
}
```

### Configuration Files

#### App Configuration
**File:** `shopify.app.toml`
```toml
# Real McCoy Returns App Configuration

name = "real-mcoy-returns"
client_id = "YOUR_CLIENT_ID"
application_url = "https://your-app-url.com"
embedded = true

[access_scopes]
scopes = "read_orders,write_orders,read_customers,write_customers,read_fulfillments,write_fulfillments,read_returns,write_returns"

[auth]
redirect_urls = [
  "https://your-app-url.com/auth/callback",
  "https://your-app-url.com/auth/shopify/callback"
]

[webhooks]
api_version = "2025-07"

[pos]
embedded = false
```

### Development Environment Setup

#### Environment Variables
**File:** `.env.example`
```env
# Shopify App Configuration
SHOPIFY_API_KEY=your_api_key_here
SHOPIFY_API_SECRET=your_api_secret_here
SCOPES=read_orders,write_orders,read_customers,write_customers,read_fulfillments,write_fulfillments,read_returns,write_returns
HOST=https://your-app-url.com

# Development Store
SHOPIFY_APP_URL=https://your-app-url.com
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret

# Database (if needed)
DATABASE_URL=your_database_url

# Session Storage
SESSION_SECRET=your_session_secret_key
```

#### Package Dependencies
**File:** `package.json`
```json
{
  "name": "real-mcoy-returns",
  "version": "1.0.0",
  "description": "Shopify Returns and Order Tracking Widget",
  "scripts": {
    "build": "remix build",
    "dev": "shopify app dev",
    "deploy": "shopify app deploy",
    "start": "remix-serve build"
  },
  "dependencies": {
    "@remix-run/node": "^2.0.0",
    "@remix-run/react": "^2.0.0",
    "@shopify/shopify-api": "^8.0.0",
    "@shopify/shopify-app-remix": "^2.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@remix-run/dev": "^2.0.0",
    "@shopify/cli": "^3.83.0",
    "@types/react": "^18.0.0",
    "typescript": "^5.0.0"
  }
}
```

### Testing Strategy

#### Unit Tests Structure
```
tests/
├── unit/
│   ├── lib/
│   │   ├── shopify-graphql.test.ts
│   │   ├── order-validation.test.ts
│   │   └── return-logic.test.ts
│   └── routes/
│       ├── api.orders.lookup.test.ts
│       └── api.returns.create.test.ts
├── integration/
│   ├── order-lookup-flow.test.ts
│   ├── return-creation-flow.test.ts
│   └── widget-interaction.test.ts
└── e2e/
    ├── complete-user-journey.test.ts
    └── responsive-design.test.ts
```

### Deployment Checklist

1. **Pre-deployment:**
   - [ ] All unit tests passing
   - [ ] Integration tests completed
   - [ ] Browser compatibility verified
   - [ ] Mobile responsiveness tested
   - [ ] Accessibility compliance checked

2. **Shopify App Setup:**
   - [ ] App created in Partner Dashboard
   - [ ] Proper scopes configured
   - [ ] Webhook endpoints registered
   - [ ] App URL and redirect URLs set

3. **Theme Extension Deployment:**
   - [ ] Extension built and tested locally
   - [ ] Theme check validation passed
   - [ ] Development store testing completed
   - [ ] Production deployment executed

4. **Post-deployment:**
   - [ ] Monitoring and error tracking enabled
   - [ ] Performance metrics baseline established
   - [ ] User documentation updated
   - [ ] Support team trained on new features

This technical specification provides the complete blueprint for implementing the Real McCoy Returns app using modern Shopify development practices, Polaris UI components, and GraphQL API integration.

<div class="real-mcoy-returns-widget" 
     data-primary-color="{{ primary_color }}"
     data-enable-returns="{{ enable_returns }}"
     data-enable-tracking="{{ enable_tracking }}">
  
  <div class="widget-header">
    <h2>{{ widget_title }}</h2>
  </div>

  <div class="widget-content">
    <!-- Order Lookup Form -->
    <div id="order-lookup-form" class="widget-step active">
      {% render 'order-lookup' %}
    </div>

    <!-- Order Display -->
    <div id="order-display" class="widget-step hidden">
      {% render 'order-display' %}
    </div>

    <!-- Tracking Information -->
    <div id="tracking-info" class="widget-step hidden">
      {% render 'tracking-info' %}
    </div>

    <!-- Return Form -->
    <div id="return-form" class="widget-step hidden">
      {% render 'return-form' %}
    </div>

    <!-- Return Label -->
    <div id="return-label" class="widget-step hidden">
      {% render 'return-label' %}
    </div>
  </div>
</div>

<script src="{{ 'widget.js' | asset_url }}" defer></script>
<link rel="stylesheet" href="{{ 'widget.css' | asset_url }}">

{% schema %}
{
  "name": "Returns & Tracking Widget",
  "target": "section",
  "settings": [
    {
      "type": "text",
      "id": "widget_title",
      "label": "Widget Title",
      "default": "Order Tracking & Returns"
    },
    {
      "type": "color",
      "id": "primary_color", 
      "label": "Primary Color",
      "default": "#1a73e8"
    },
    {
      "type": "checkbox",
      "id": "enable_returns",
      "label": "Enable Returns",
      "default": true
    },
    {
      "type": "checkbox", 
      "id": "enable_tracking",
      "label": "Enable Order Tracking",
      "default": true
    },
    {
      "type": "range",
      "id": "return_window_days",
      "label": "Return Window (Days)",
      "min": 7,
      "max": 90,
      "step": 1,
      "default": 30
    }
  ]
}
{% endschema %}
```

#### Widget JavaScript Class
**File:** `extensions/real-mcoy-returns-widget/assets/widget.js`
```javascript
/**
 * Real McCoy Returns Widget
 * Handles order lookup, tracking, and returns functionality
 */
class RealMcCoyReturnsWidget {
  constructor(element) {
    this.element = element;
    this.currentStep = 'order-lookup-form';
    this.orderData = null;
    this.returnItems = [];
    
    this.init();
  }

  init() {
    this.bindEvents();
    this.setupFormValidation();
  }

  bindEvents() {
    // Order lookup form submission
    const lookupForm = this.element.querySelector('#order-lookup-form form');
    if (lookupForm) {
      lookupForm.addEventListener('submit', this.handleOrderLookup.bind(this));
    }

    // Return form submission  
    const returnForm = this.element.querySelector('#return-form form');
    if (returnForm) {
      returnForm.addEventListener('submit', this.handleReturnSubmission.bind(this));
    }

    // Navigation buttons
    this.element.addEventListener('click', (e) => {
      if (e.target.matches('[data-action="back"]')) {
        this.navigateBack();
      } else if (e.target.matches('[data-action="track-order"]')) {
        this.showTrackingInfo();
      } else if (e.target.matches('[data-action="request-return"]')) {
        this.showReturnForm();
      }
    });
  }

  async handleOrderLookup(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const email = formData.get('email');
    const orderNumber = formData.get('order_number');

    this.showLoading(true);

    try {
      const response = await fetch('/api/orders/lookup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, orderNumber })
      });

      const result = await response.json();

      if (result.success) {
        this.orderData = result.order;
        this.showOrderDetails();
      } else {
        this.showError(result.error || 'Order not found. Please check your email and order number.');
      }
    } catch (error) {
      this.showError('Unable to lookup order. Please try again.');
    } finally {
      this.showLoading(false);
    }
  }

  async handleReturnSubmission(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const returnData = {
      orderId: this.orderData.id,
      items: this.returnItems,
      reason: formData.get('return_reason'),
      comments: formData.get('comments')
    };

    this.showLoading(true);

    try {
      const response = await fetch('/api/returns/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(returnData)
      });

      const result = await response.json();

      if (result.success) {
        this.showReturnLabel(result.return);
      } else {
        this.showError(result.error || 'Unable to create return request.');
      }
    } catch (error) {
      this.showError('Unable to submit return request. Please try again.');
    } finally {
      this.showLoading(false);
    }
  }

  showStep(stepId) {
    // Hide all steps
    this.element.querySelectorAll('.widget-step').forEach(step => {
      step.classList.add('hidden');
      step.classList.remove('active');
    });

    // Show target step
    const targetStep = this.element.querySelector(`#${stepId}`);
    if (targetStep) {
      targetStep.classList.remove('hidden');
      targetStep.classList.add('active');
      this.currentStep = stepId;
    }
  }

  showOrderDetails() {
    this.populateOrderData();
    this.showStep('order-display');
  }

  showTrackingInfo() {
    this.populateTrackingData();
    this.showStep('tracking-info');
  }

  showReturnForm() {
    this.populateReturnForm();
    this.showStep('return-form');
  }

  showReturnLabel(returnData) {
    this.populateReturnLabel(returnData);
    this.showStep('return-label');
  }

  navigateBack() {
    // Simple navigation logic - can be enhanced
    switch (this.currentStep) {
      case 'order-display':
        this.showStep('order-lookup-form');
        break;
      case 'tracking-info':
      case 'return-form':
        this.showStep('order-display');
        break;
      case 'return-label':
        this.showStep('return-form');
        break;
    }
  }

  populateOrderData() {
    const container = this.element.querySelector('#order-display');
    if (!container || !this.orderData) return;

    // Populate order details using Polaris-style HTML
    container.innerHTML = `
      <div class="order-summary">
        <h3>Order ${this.orderData.name}</h3>
        <p>Order Date: ${new Date(this.orderData.createdAt).toLocaleDateString()}</p>
        <p>Total: ${this.orderData.totalPriceSet.presentmentMoney.amount} ${this.orderData.totalPriceSet.presentmentMoney.currencyCode}</p>
        <p>Status: <span class="status-badge">${this.orderData.displayFulfillmentStatus}</span></p>
      </div>
      
      <div class="order-items">
        <h4>Items Ordered</h4>
        ${this.orderData.lineItems.edges.map(edge => `
          <div class="order-item">
            <img src="${edge.node.variant?.image?.url || ''}" alt="${edge.node.title}" width="60" height="60">
            <div class="item-details">
              <h5>${edge.node.title}</h5>
              <p>Quantity: ${edge.node.quantity}</p>
              <p>Price: ${edge.node.originalTotalSet.presentmentMoney.amount} ${edge.node.originalTotalSet.presentmentMoney.currencyCode}</p>
            </div>
          </div>
        `).join('')}
      </div>

      <div class="order-actions">
        <button type="button" data-action="track-order" class="btn btn-secondary">Track Order</button>
        <button type="button" data-action="request-return" class="btn btn-primary">Request Return</button>
        <button type="button" data-action="back" class="btn btn-link">← Back to Search</button>
      </div>
    `;
  }

  showLoading(show) {
    const existingLoader = this.element.querySelector('.loading-spinner');
    
    if (show && !existingLoader) {
      const loader = document.createElement('div');
      loader.className = 'loading-spinner';
      loader.innerHTML = '<div class="spinner"></div><p>Loading...</p>';
      this.element.appendChild(loader);
    } else if (!show && existingLoader) {
      existingLoader.remove();
    }
  }

  showError(message) {
    const errorContainer = this.element.querySelector('.error-message') || 
                          this.createElement('div', 'error-message');
    
    errorContainer.innerHTML = `
      <div class="error-banner">
        <p>${message}</p>
        <button type="button" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    if (!errorContainer.parentElement) {
      this.element.insertBefore(errorContainer, this.element.firstChild);
    }
  }

  createElement(tag, className) {
    const element = document.createElement(tag);
    if (className) element.className = className;
    return element;
  }
}

// Initialize widget when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const widgets = document.querySelectorAll('.real-mcoy-returns-widget');
  widgets.forEach(widget => new RealMcCoyReturnsWidget(widget));
});
```

### Backend Implementation

#### GraphQL Client Setup
**File:** `app/lib/shopify-graphql.ts`
```typescript
import { GraphqlQueryError } from "@shopify/shopify-api";

export interface OrderLookupParams {
  email: string;
  orderNumber: string;
}

export interface OrderData {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  displayFulfillmentStatus: string;
  totalPriceSet: {
    presentmentMoney: {
      amount: string;
      currencyCode: string;
    };
  };
  lineItems: {
    edges: Array<{
      node: {
        id: string;
        title: string;
        quantity: number;
        variant?: {
          id: string;
          image?: {
            url: string;
          };
        };
        originalTotalSet: {
          presentmentMoney: {
            amount: string;
            currencyCode: string;
          };
        };
      };
    }>;
  };
  fulfillments: {
    edges: Array<{
      node: {
        id: string;
        status: string;
        trackingCompany?: string;
        trackingNumber?: string;
        trackingUrl?: string;
      };
    }>;
  };
}

const ORDER_LOOKUP_QUERY = `
  query orderLookup($query: String!) {
    orders(first: 1, query: $query) {
      edges {
        node {
          id
          name
          email
          createdAt
          displayFulfillmentStatus
          totalPriceSet {
            presentmentMoney {
              amount
              currencyCode
            }
          }
          lineItems(first: 50) {
            edges {
              node {
                id
                title 
                quantity
                variant {
                  id
                  image {
                    url
                  }
                }
                originalTotalSet {
                  presentmentMoney {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
          fulfillments(first: 10) {
            edges {
              node {
                id
                status
                trackingCompany
                trackingNumber
                trackingUrl
              }
            }
          }
          shippingAddress {
            firstName
            lastName
            address1
            address2
            city
            province
            country
            zip
          }
        }
      }
    }
  }
`;

const RETURN_CREATE_MUTATION = `
  mutation returnCreate($input: ReturnInput!) {
    returnCreate(input: $input) {
      return {
        id
        status
        returnLineItems(first: 50) {
          edges {
            node {
              id
              quantity
              returnReason
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export async function lookupOrder(
  graphql: any,
  { email, orderNumber }: OrderLookupParams
): Promise<{ success: boolean; order?: OrderData; error?: string }> {
  try {
    // Clean order number (remove # if present)
    const cleanOrderNumber = orderNumber.replace(/^#/, '');
    
    // Build search query - search by order name and email
    const searchQuery = `name:${cleanOrderNumber} AND email:${email}`;
    
    const response = await graphql(ORDER_LOOKUP_QUERY, {
      variables: { query: searchQuery }
    });

    const orders = response.data?.orders?.edges || [];
    
    if (orders.length === 0) {
      return {
        success: false,
        error: "Order not found. Please check your email address and order number."
      };
    }

    const order = orders[0].node;
    
    // Verify email matches (case insensitive)
    if (order.email?.toLowerCase() !== email.toLowerCase()) {
      return {
        success: false,
        error: "Order not found. Please check your email address and order number."
      };
    }

    return {
      success: true,
      order: order as OrderData
    };

  } catch (error) {
    console.error('Order lookup error:', error);
    
    if (error instanceof GraphqlQueryError) {
      return {
        success: false,
        error: "Unable to lookup order. Please try again."
      };
    }
    
    return {
      success: false,
      error: "An unexpected error occurred. Please try again."
    };
  }
}

export async function createReturn(
  graphql: any,
  returnData: {
    orderId: string;
    items: Array<{
      lineItemId: string;
      quantity: number;
      reason: string;
    }>;
    reason: string;
    comments?: string;
  }
): Promise<{ success: boolean; return?: any; error?: string }> {
  try {
    const returnLineItems = returnData.items.map(item => ({
      lineItemId: item.lineItemId,
      quantity: item.quantity,
      returnReason: item.reason
    }));

    const response = await graphql(RETURN_CREATE_MUTATION, {
      variables: {
        input: {
          orderId: returnData.orderId,
          returnLineItems,
          note: returnData.comments
        }
      }
    });

    const result = response.data?.returnCreate;
    
    if (result?.userErrors?.length > 0) {
      return {
        success: false,
        error: result.userErrors[0].message
      };
    }

    return {
      success: true,
      return: result?.return
    };

  } catch (error) {
    console.error('Return creation error:', error);
    return {
      success: false,
      error: "Unable to create return request. Please try again."
    };
  }
}