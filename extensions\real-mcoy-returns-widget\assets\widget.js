/**
 * Real McCoy Returns Widget
 * Handles order lookup, tracking, and returns functionality
 */
class RealMcCoyReturnsWidget {
  constructor(element) {
    this.element = element;
    this.currentStep = 'order-lookup-form';
    this.orderData = null;
    this.returnItems = [];
    this.settings = this.getSettings();
    
    this.init();
  }

  init() {
    this.bindEvents();
    this.setupFormValidation();
    this.restoreFormState();
  }

  getSettings() {
    return {
      primaryColor: this.element.dataset.primaryColor || '#1a73e8',
      enableReturns: this.element.dataset.enableReturns === 'true',
      enableTracking: this.element.dataset.enableTracking === 'true',
      returnWindow: parseInt(this.element.dataset.returnWindow) || 30
    };
  }

  bindEvents() {
    // Order lookup form submission
    const lookupForm = this.element.querySelector('#lookup-form');
    if (lookupForm) {
      lookupForm.addEventListener('submit', this.handleOrderLookup.bind(this));
    }

    // Return form submission  
    const returnForm = this.element.querySelector('#return-form');
    if (returnForm) {
      returnForm.addEventListener('submit', this.handleReturnSubmission.bind(this));
    }

    // Navigation buttons
    this.element.addEventListener('click', (e) => {
      const action = e.target.closest('[data-action]')?.dataset.action;
      
      switch (action) {
        case 'back':
          this.navigateBack();
          break;
        case 'track-order':
          this.showTrackingInfo();
          break;
        case 'request-return':
          this.showReturnForm();
          break;
        case 'refresh-tracking':
          this.refreshTracking();
          break;
        case 'print-confirmation':
          this.printConfirmation();
          break;
        case 'new-lookup':
          this.resetWidget();
          break;
      }
    });

    // Return item selection
    this.element.addEventListener('change', (e) => {
      if (e.target.matches('.return-item-checkbox')) {
        this.updateReturnItems();
      }
    });
  }

  setupFormValidation() {
    // Real-time email validation
    const emailInput = this.element.querySelector('#email');
    if (emailInput) {
      emailInput.addEventListener('blur', this.validateEmail.bind(this));
      emailInput.addEventListener('input', this.clearFieldError.bind(this));
    }

    // Real-time order number validation
    const orderInput = this.element.querySelector('#order_number');
    if (orderInput) {
      orderInput.addEventListener('blur', this.validateOrderNumber.bind(this));
      orderInput.addEventListener('input', this.clearFieldError.bind(this));
    }
  }

  async handleOrderLookup(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const email = formData.get('email').trim();
    const orderNumber = formData.get('order_number').trim();

    // Client-side validation
    if (!this.validateForm({ email, orderNumber })) {
      return;
    }

    this.showLoading(true, 'lookup-btn');
    this.clearErrors();

    try {
      const response = await fetch('/api/orders/lookup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, orderNumber })
      });

      const result = await response.json();

      if (result.success) {
        this.orderData = result.order;
        this.saveFormState();
        this.showOrderDetails();
      } else {
        this.showError(result.error || 'Order not found. Please check your email and order number.', 'lookup-error');
      }
    } catch (error) {
      console.error('Order lookup error:', error);
      this.showError('Unable to lookup order. Please check your connection and try again.', 'lookup-error');
    } finally {
      this.showLoading(false, 'lookup-btn');
    }
  }

  async handleReturnSubmission(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const returnReason = formData.get('return_reason');
    const comments = formData.get('comments');

    // Validate return items selection
    if (this.returnItems.length === 0) {
      this.showError('Please select at least one item to return.', 'return-error');
      return;
    }

    if (!returnReason) {
      this.showError('Please select a reason for your return.', 'return-error');
      return;
    }

    this.showLoading(true, 'submit-return-btn');
    this.clearErrors();

    try {
      const returnData = {
        orderId: this.orderData.id,
        items: this.returnItems,
        reason: returnReason,
        comments: comments || ''
      };

      const response = await fetch('/api/returns/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(returnData)
      });

      const result = await response.json();

      if (result.success) {
        this.returnData = result.return;
        this.showReturnConfirmation();
      } else {
        this.showError(result.error || 'Unable to create return request. Please try again.', 'return-error');
      }
    } catch (error) {
      console.error('Return submission error:', error);
      this.showError('Unable to submit return request. Please check your connection and try again.', 'return-error');
    } finally {
      this.showLoading(false, 'submit-return-btn');
    }
  }

  showStep(stepId) {
    // Hide all steps
    this.element.querySelectorAll('.widget-step').forEach(step => {
      step.classList.add('hidden');
      step.classList.remove('active');
    });

    // Show target step
    const targetStep = this.element.querySelector(`#${stepId}`);
    if (targetStep) {
      targetStep.classList.remove('hidden');
      targetStep.classList.add('active');
      this.currentStep = stepId;
      
      // Focus management for accessibility
      const focusTarget = targetStep.querySelector('h2, h3, input, button');
      if (focusTarget) {
        focusTarget.focus();
      }
    }
  }

  showOrderDetails() {
    this.populateOrderData();
    this.showStep('order-display');
  }

  showTrackingInfo() {
    if (!this.settings.enableTracking) {
      this.showError('Order tracking is not available.', 'tracking-error');
      return;
    }
    this.populateTrackingData();
    this.showStep('tracking-info');
  }

  showReturnForm() {
    if (!this.settings.enableReturns) {
      this.showError('Returns are not available for this order.', 'return-error');
      return;
    }
    this.populateReturnForm();
    this.showStep('return-form');
  }

  showReturnConfirmation() {
    this.populateReturnConfirmation();
    this.showStep('return-confirmation');
  }

  navigateBack() {
    switch (this.currentStep) {
      case 'order-display':
        this.showStep('order-lookup-form');
        break;
      case 'tracking-info':
      case 'return-form':
        this.showStep('order-display');
        break;
      case 'return-confirmation':
        this.showStep('return-form');
        break;
    }
  }

  populateOrderData() {
    const container = this.element.querySelector('#order-summary');
    if (!container || !this.orderData) return;

    const orderDate = new Date(this.orderData.createdAt).toLocaleDateString();
    const totalAmount = this.orderData.totalPriceSet.presentmentMoney.amount;
    const currency = this.orderData.totalPriceSet.presentmentMoney.currencyCode;

    container.innerHTML = `
      <div class="order-header">
        <h3>Order ${this.orderData.name}</h3>
        <span class="status-badge status-${this.orderData.displayFulfillmentStatus.toLowerCase().replace(' ', '-')}">
          ${this.orderData.displayFulfillmentStatus}
        </span>
      </div>
      <div class="order-meta">
        <p><strong>Order Date:</strong> ${orderDate}</p>
        <p><strong>Total:</strong> ${totalAmount} ${currency}</p>
        <p><strong>Email:</strong> ${this.orderData.email}</p>
      </div>
    `;

    this.populateOrderItems();
  }

  populateOrderItems() {
    const container = this.element.querySelector('#items-list');
    if (!container || !this.orderData) return;

    const itemsHTML = this.orderData.lineItems.edges.map(edge => {
      const item = edge.node;
      const itemTotal = item.originalTotalSet.presentmentMoney.amount;
      const currency = item.originalTotalSet.presentmentMoney.currencyCode;
      const imageUrl = item.variant?.image?.url || '';

      return `
        <div class="order-item" data-line-item-id="${item.id}">
          ${imageUrl ? `<img src="${imageUrl}" alt="${item.title}" class="item-image" width="60" height="60">` : ''}
          <div class="item-details">
            <h4>${item.title}</h4>
            <p class="item-quantity">Quantity: ${item.quantity}</p>
            <p class="item-price">Price: ${itemTotal} ${currency}</p>
          </div>
        </div>
      `;
    }).join('');

    container.innerHTML = itemsHTML;
  }

  populateTrackingData() {
    const statusContainer = this.element.querySelector('#tracking-status');
    const timelineContainer = this.element.querySelector('#tracking-timeline');
    const detailsContainer = this.element.querySelector('#fulfillment-details');

    if (!statusContainer || !this.orderData) return;

    // Populate tracking status
    statusContainer.innerHTML = `
      <div class="tracking-overview">
        <h4>Current Status</h4>
        <div class="status-display">
          <span class="status-badge status-${this.orderData.displayFulfillmentStatus.toLowerCase().replace(' ', '-')}">
            ${this.orderData.displayFulfillmentStatus}
          </span>
        </div>
      </div>
    `;

    // Populate fulfillment details
    if (this.orderData.fulfillments.edges.length > 0) {
      const fulfillmentsHTML = this.orderData.fulfillments.edges.map(edge => {
        const fulfillment = edge.node;
        return `
          <div class="fulfillment-item">
            <h5>Shipment ${fulfillment.id.split('/').pop()}</h5>
            <p><strong>Status:</strong> ${fulfillment.status}</p>
            ${fulfillment.trackingCompany ? `<p><strong>Carrier:</strong> ${fulfillment.trackingCompany}</p>` : ''}
            ${fulfillment.trackingNumber ? `<p><strong>Tracking Number:</strong> ${fulfillment.trackingNumber}</p>` : ''}
            ${fulfillment.trackingUrl ? `<p><a href="${fulfillment.trackingUrl}" target="_blank" class="tracking-link">Track Package</a></p>` : ''}
          </div>
        `;
      }).join('');

      detailsContainer.innerHTML = `
        <h4>Shipment Details</h4>
        <div class="fulfillments-list">
          ${fulfillmentsHTML}
        </div>
      `;
    } else {
      detailsContainer.innerHTML = `
        <div class="no-fulfillments">
          <p>No shipment information available yet.</p>
        </div>
      `;
    }
  }

  populateReturnForm() {
    const container = this.element.querySelector('#return-items-list');
    if (!container || !this.orderData) return;

    // Only show items that can be returned (fulfilled items)
    const returnableItems = this.orderData.lineItems.edges.filter(edge => {
      // In a real implementation, you'd check fulfillment status and return eligibility
      return true; // For demo purposes, all items are returnable
    });

    const itemsHTML = returnableItems.map(edge => {
      const item = edge.node;
      const itemTotal = item.originalTotalSet.presentmentMoney.amount;
      const currency = item.originalTotalSet.presentmentMoney.currencyCode;
      const imageUrl = item.variant?.image?.url || '';

      return `
        <div class="return-item">
          <div class="return-item-header">
            <label class="return-item-label">
              <input type="checkbox" class="return-item-checkbox" data-line-item-id="${item.id}" data-max-quantity="${item.quantity}">
              <span class="checkbox-custom"></span>
              ${item.title}
            </label>
          </div>
          <div class="return-item-content">
            ${imageUrl ? `<img src="${imageUrl}" alt="${item.title}" class="return-item-image" width="50" height="50">` : ''}
            <div class="return-item-info">
              <p class="item-price">${itemTotal} ${currency}</p>
              <div class="quantity-selector hidden">
                <label for="qty-${item.id}">Quantity to return:</label>
                <select id="qty-${item.id}" class="return-quantity-select" data-line-item-id="${item.id}">
                  ${Array.from({length: item.quantity}, (_, i) => `<option value="${i + 1}">${i + 1}</option>`).join('')}
                </select>
              </div>
            </div>
          </div>
        </div>
      `;
    }).join('');

    container.innerHTML = itemsHTML;
  }

  populateReturnConfirmation() {
    const container = this.element.querySelector('#return-confirmation-details');
    if (!container || !this.returnData) return;

    container.innerHTML = `
      <div class="return-summary">
        <h4>Return Summary</h4>
        <div class="return-info">
          <p><strong>Return ID:</strong> ${this.returnData.id.split('/').pop()}</p>
          <p><strong>Status:</strong> ${this.returnData.status}</p>
          <p><strong>Items:</strong> ${this.returnItems.length} item(s)</p>
        </div>
      </div>
      
      <div class="return-items-summary">
        <h5>Items Being Returned:</h5>
        <ul class="return-items-list">
          ${this.returnItems.map(item => `
            <li>Line Item ${item.lineItemId.split('/').pop()} - Quantity: ${item.quantity}</li>
          `).join('')}
        </ul>
      </div>
    `;
  }

  updateReturnItems() {
    this.returnItems = [];
    const checkboxes = this.element.querySelectorAll('.return-item-checkbox:checked');
    
    checkboxes.forEach(checkbox => {
      const lineItemId = checkbox.dataset.lineItemId;
      const quantitySelect = this.element.querySelector(`#qty-${lineItemId.split('/').pop()}`);
      const quantity = quantitySelect ? parseInt(quantitySelect.value) : 1;
      
      this.returnItems.push({
        lineItemId,
        quantity,
        reason: 'defective' // Will be updated from form
      });
    });

    // Show/hide quantity selectors
    checkboxes.forEach(checkbox => {
      const quantitySelector = checkbox.closest('.return-item').querySelector('.quantity-selector');
      quantitySelector.classList.toggle('hidden', !checkbox.checked);
    });

    // Update form validation
    const submitBtn = this.element.querySelector('#submit-return-btn');
    if (submitBtn) {
      submitBtn.disabled = this.returnItems.length === 0;
    }
  }

  validateForm(data) {
    let isValid = true;

    if (!this.validateEmail(data.email)) {
      isValid = false;
    }

    if (!this.validateOrderNumber(data.orderNumber)) {
      isValid = false;
    }

    return isValid;
  }

  validateEmail(email) {
    const emailInput = this.element.querySelector('#email');
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email || !emailRegex.test(email)) {
      this.showFieldError(emailInput, 'Please enter a valid email address');
      return false;
    }
    
    this.clearFieldError(emailInput);
    return true;
  }

  validateOrderNumber(orderNumber) {
    const orderInput = this.element.querySelector('#order_number');
    const cleaned = orderNumber.replace(/^#/, '');
    
    if (!cleaned || !/^\d+$/.test(cleaned)) {
      this.showFieldError(orderInput, 'Please enter a valid order number');
      return false;
    }
    
    this.clearFieldError(orderInput);
    return true;
  }

  showFieldError(input, message) {
    if (!input) return;
    
    input.classList.add('error');
    input.setAttribute('aria-invalid', 'true');
    
    let errorElement = input.parentNode.querySelector('.field-error');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'field-error';
      errorElement.setAttribute('role', 'alert');
      input.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
  }

  clearFieldError(input) {
    if (!input) return;
    
    input.classList.remove('error');
    input.setAttribute('aria-invalid', 'false');
    
    const errorElement = input.parentNode.querySelector('.field-error');
    if (errorElement) {
      errorElement.remove();
    }
  }

  showLoading(show, buttonId) {
    const button = this.element.querySelector(`#${buttonId}`);
    if (!button) return;

    const text = button.querySelector('.btn-text');
    const spinner = button.querySelector('.btn-spinner');
    
    if (show) {
      button.disabled = true;
      if (text) text.classList.add('hidden');
      if (spinner) spinner.classList.remove('hidden');
    } else {
      button.disabled = false;
      if (text) text.classList.remove('hidden');
      if (spinner) spinner.classList.add('hidden');
    }
  }

  showError(message, containerId) {
    const container = this.element.querySelector(`#${containerId}`);
    if (!container) return;
    
    container.innerHTML = `
      <div class="error-banner" role="alert">
        <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <p>${message}</p>
        <button type="button" class="error-close" onclick="this.parentElement.parentElement.innerHTML = ''">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
      </div>
    `;
    
    // Scroll error into view
    container.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }

  clearErrors() {
    this.element.querySelectorAll('.error-container').forEach(container => {
      container.innerHTML = '';
    });
  }

  saveFormState() {
    const state = {
      step: this.currentStep,
      orderData: this.orderData,
      returnItems: this.returnItems,
      timestamp: Date.now()
    };
    
    try {
      sessionStorage.setItem('mcoy-returns-state', JSON.stringify(state));
    } catch (e) {
      console.warn('Unable to save form state:', e);
    }
  }

  restoreFormState() {
    try {
      const saved = sessionStorage.getItem('mcoy-returns-state');
      if (!saved) return false;
      
      const state = JSON.parse(saved);
      
      // Check if state is fresh (< 30 minutes)
      if (Date.now() - state.timestamp > 1800000) {
        sessionStorage.removeItem('mcoy-returns-state');
        return false;
      }
      
      this.orderData = state.orderData;
      this.returnItems = state.returnItems;
      
      if (state.step !== 'order-lookup-form') {
        this.showStep(state.step);
      }
      
      return true;
    } catch (e) {
      console.warn('Unable to restore form state:', e);
      return false;
    }
  }

  resetWidget() {
    this.orderData = null;
    this.returnItems = [];
    this.returnData = null;
    this.showStep('order-lookup-form');
    this.clearErrors();
    
    // Clear form inputs
    this.element.querySelectorAll('input, select, textarea').forEach(input => {
      if (input.type === 'checkbox' || input.type === 'radio') {
        input.checked = false;
      } else {
        input.value = '';
      }
    });
    
    // Clear session storage
    try {
      sessionStorage.removeItem('mcoy-returns-state');
    } catch (e) {
      console.warn('Unable to clear form state:', e);
    }
  }

  refreshTracking() {
    // In a real implementation, this would refetch tracking data
    this.populateTrackingData();
  }

  printConfirmation() {
    const confirmationContent = this.element.querySelector('#return-confirmation-details').innerHTML;
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
      <html>
        <head>
          <title>Return Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .return-summary { margin-bottom: 20px; }
            .return-info p { margin: 5px 0; }
          </style>
        </head>
        <body>
          <h1>Return Confirmation</h1>
          ${confirmationContent}
          <script>window.print(); window.close();</script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
  }
}

// Initialize widget when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const widgets = document.querySelectorAll('.real-mcoy-returns-widget');
  widgets.forEach(widget => new RealMcCoyReturnsWidget(widget));
});