import { lookupOrder, createReturn, validateEmail, validateOrderNumber } from '../../app/lib/shopify-graphql';

describe('shopify-graphql', () => {
  describe('validateEmail', () => {
    test('validates correct email formats', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    test('rejects invalid email formats', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });
  });

  describe('validateOrderNumber', () => {
    test('validates correct order number formats', () => {
      expect(validateOrderNumber('1001')).toBe(true);
      expect(validateOrderNumber('#1001')).toBe(true);
      expect(validateOrderNumber('12345')).toBe(true);
    });

    test('rejects invalid order number formats', () => {
      expect(validateOrderNumber('abc')).toBe(false);
      expect(validateOrderNumber('123abc')).toBe(false);
      expect(validateOrderNumber('')).toBe(false);
      expect(validateOrderNumber('#')).toBe(false);
    });
  });

  describe('lookupOrder', () => {
    const mockGraphql = jest.fn();

    beforeEach(() => {
      mockGraphql.mockClear();
    });

    test('returns order when found', async () => {
      const mockOrder = {
        id: 'gid://shopify/Order/123',
        name: '#1001',
        email: '<EMAIL>',
        displayFulfillmentStatus: 'fulfilled',
        totalPriceSet: {
          presentmentMoney: {
            amount: '99.99',
            currencyCode: 'USD'
          }
        },
        lineItems: { edges: [] },
        fulfillments: { edges: [] }
      };

      mockGraphql.mockResolvedValue({
        data: {
          orders: {
            edges: [{ node: mockOrder }]
          }
        }
      });

      const result = await lookupOrder(mockGraphql, {
        email: '<EMAIL>',
        orderNumber: '1001'
      });

      expect(result.success).toBe(true);
      expect(result.order).toEqual(mockOrder);
      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query orderLookup'),
        expect.objectContaining({
          variables: { query: 'name:1001 AND email:<EMAIL>' }
        })
      );
    });

    test('returns error when order not found', async () => {
      mockGraphql.mockResolvedValue({
        data: {
          orders: {
            edges: []
          }
        }
      });

      const result = await lookupOrder(mockGraphql, {
        email: '<EMAIL>',
        orderNumber: '1001'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Order not found. Please check your email address and order number.');
    });

    test('returns error when email does not match', async () => {
      const mockOrder = {
        email: '<EMAIL>'
      };

      mockGraphql.mockResolvedValue({
        data: {
          orders: {
            edges: [{ node: mockOrder }]
          }
        }
      });

      const result = await lookupOrder(mockGraphql, {
        email: '<EMAIL>',
        orderNumber: '1001'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Order not found. Please check your email address and order number.');
    });

    test('handles GraphQL errors', async () => {
      mockGraphql.mockRejectedValue(new Error('GraphQL Error'));

      const result = await lookupOrder(mockGraphql, {
        email: '<EMAIL>',
        orderNumber: '1001'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('An unexpected error occurred. Please try again.');
    });
  });

  describe('createReturn', () => {
    const mockGraphql = jest.fn();

    beforeEach(() => {
      mockGraphql.mockClear();
    });

    test('creates return successfully', async () => {
      const mockReturn = {
        id: 'gid://shopify/Return/456',
        status: 'open',
        returnLineItems: { edges: [] }
      };

      mockGraphql.mockResolvedValue({
        data: {
          returnCreate: {
            return: mockReturn,
            userErrors: []
          }
        }
      });

      const result = await createReturn(mockGraphql, {
        orderId: 'gid://shopify/Order/123',
        items: [
          {
            lineItemId: 'gid://shopify/LineItem/789',
            quantity: 1,
            reason: 'defective'
          }
        ],
        reason: 'defective',
        comments: 'Item was damaged'
      });

      expect(result.success).toBe(true);
      expect(result.return).toEqual(mockReturn);
    });

    test('handles user errors from GraphQL', async () => {
      mockGraphql.mockResolvedValue({
        data: {
          returnCreate: {
            return: null,
            userErrors: [
              {
                field: 'items',
                message: 'Items cannot be returned'
              }
            ]
          }
        }
      });

      const result = await createReturn(mockGraphql, {
        orderId: 'gid://shopify/Order/123',
        items: [
          {
            lineItemId: 'gid://shopify/LineItem/789',
            quantity: 1,
            reason: 'defective'
          }
        ],
        reason: 'defective'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Items cannot be returned');
    });

    test('handles GraphQL errors', async () => {
      mockGraphql.mockRejectedValue(new Error('Network Error'));

      const result = await createReturn(mockGraphql, {
        orderId: 'gid://shopify/Order/123',
        items: [
          {
            lineItemId: 'gid://shopify/LineItem/789',
            quantity: 1,
            reason: 'defective'
          }
        ],
        reason: 'defective'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Unable to create return request. Please try again.');
    });
  });
});