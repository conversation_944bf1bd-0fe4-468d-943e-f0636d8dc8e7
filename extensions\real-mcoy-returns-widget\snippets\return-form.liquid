<div class="return-form-container">
  <div class="return-header">
    <h3>Request a Return</h3>
    <button type="button" data-action="back" class="btn btn-link back-btn">
      <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M19 12H5M12 19l-7-7 7-7"/>
      </svg>
      Back to Order
    </button>
  </div>
  
  <form class="return-form" id="return-form" method="post">
    <div class="returnable-items">
      <h4>Select Items to Return</h4>
      <div id="return-items-list" class="return-items-grid">
        <!-- Items will be populated by JavaScript -->
      </div>
    </div>
    
    <div class="return-details">
      <div class="form-group">
        <label for="return_reason" class="form-label">
          Reason for Return
          <span class="required" aria-label="required">*</span>
        </label>
        <select id="return_reason" name="return_reason" required class="form-select" aria-describedby="reason-help">
          <option value="">Select a reason</option>
          <option value="defective">Defective/Damaged</option>
          <option value="wrong_size">Wrong Size</option>
          <option value="wrong_item">Wrong Item Received</option>
          <option value="not_as_described">Not as Described</option>
          <option value="changed_mind">Changed Mind</option>
          <option value="other">Other</option>
        </select>
        <small id="reason-help" class="help-text">
          Please select the primary reason for your return
        </small>
      </div>
      
      <div class="form-group">
        <label for="comments" class="form-label">
          Additional Comments <span class="optional">(Optional)</span>
        </label>
        <textarea 
          id="comments" 
          name="comments" 
          rows="3" 
          placeholder="Please provide additional details about your return..."
          class="form-textarea"
          aria-describedby="comments-help"
        ></textarea>
        <small id="comments-help" class="help-text">
          Help us process your return faster by providing additional details
        </small>
      </div>
    </div>
    
    <div class="return-policy">
      <div class="policy-notice">
        <svg class="info-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <path d="M12 16v-4M12 8h.01"/>
        </svg>
        <div class="policy-text">
          <p><strong>Return Policy:</strong> Items must be returned within {{ return_window_days | default: 30 }} days of delivery in original condition with tags attached.</p>
        </div>
      </div>
    </div>
    
    <div class="form-actions">
      <button type="submit" class="btn btn-primary" id="submit-return-btn">
        <span class="btn-text">Submit Return Request</span>
        <span class="btn-spinner hidden">
          <svg class="spinner" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" opacity="0.25"/>
            <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" opacity="0.75"/>
          </svg>
        </span>
      </button>
    </div>
  </form>

  <div class="error-container" id="return-error" role="alert" aria-live="polite"></div>
</div>