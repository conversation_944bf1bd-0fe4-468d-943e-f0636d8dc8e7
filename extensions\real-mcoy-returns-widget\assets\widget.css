/* <PERSON> McCoy Returns Widget Styles */
/* Polaris-inspired design system with CSS custom properties */

.real-mcoy-returns-widget {
  /* Polaris Design Tokens */
  --p-color-surface: #ffffff;
  --p-color-text: #202223;
  --p-color-text-subdued: #6d7175;
  --p-color-border: #c9cccf;
  --p-color-border-subdued: #e1e3e5;
  --p-color-border-interactive: #005bd3;
  --p-color-bg-interactive: #006eff;
  --p-color-bg-interactive-hover: #0052cc;
  --p-color-text-on-interactive: #ffffff;
  --p-color-surface-subdued: #f6f6f7;
  --p-color-surface-hovered: #f1f2f3;
  --p-color-bg-success: #008060;
  --p-color-bg-success-subdued: #d1f7ee;
  --p-color-text-success: #004c3b;
  --p-color-bg-critical: #d72c0d;
  --p-color-bg-critical-subdued: #fbeae5;
  --p-color-text-critical: #9c1006;
  --p-color-bg-warning: #ffc453;
  --p-color-bg-warning-subdued: #fff5e6;
  --p-color-text-warning: #916a00;
  
  /* Widget-specific variables */
  --widget-primary: var(--p-color-bg-interactive);
  --widget-border-radius: 8px;
  --widget-spacing: 1rem;
  --widget-spacing-large: 1.5rem;
  --widget-spacing-small: 0.5rem;
  
  /* Base widget styles */
  max-width: 600px;
  margin: 2rem auto;
  padding: var(--widget-spacing-large);
  border: 1px solid var(--p-color-border-subdued);
  border-radius: var(--widget-border-radius);
  background: var(--p-color-surface);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--p-color-text);
  box-sizing: border-box;
}

/* Reset and isolate widget styles */
.real-mcoy-returns-widget *,
.real-mcoy-returns-widget *::before,
.real-mcoy-returns-widget *::after {
  box-sizing: border-box;
}

/* Widget header */
.widget-header {
  margin-bottom: var(--widget-spacing-large);
  text-align: center;
}

.widget-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--p-color-text);
}

/* Step management */
.widget-step {
  transition: opacity 0.3s ease-in-out;
}

.widget-step.hidden {
  display: none;
}

.widget-step.active {
  display: block;
}

/* Form styles */
.form-group {
  margin-bottom: var(--widget-spacing-large);
}

.form-label {
  display: block;
  margin-bottom: var(--widget-spacing-small);
  font-weight: 500;
  color: var(--p-color-text);
  font-size: 0.875rem;
}

.required {
  color: var(--p-color-bg-critical);
  margin-left: 2px;
}

.optional {
  color: var(--p-color-text-subdued);
  font-weight: 400;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--p-color-border);
  border-radius: 4px;
  font-size: 1rem;
  font-family: inherit;
  background: var(--p-color-surface);
  color: var(--p-color-text);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--p-color-border-interactive);
  box-shadow: 0 0 0 2px rgba(0, 107, 255, 0.2);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--p-color-bg-critical);
}

.form-input[aria-invalid="true"],
.form-select[aria-invalid="true"],
.form-textarea[aria-invalid="true"] {
  border-color: var(--p-color-bg-critical);
}

.help-text {
  display: block;
  margin-top: var(--widget-spacing-small);
  font-size: 0.875rem;
  color: var(--p-color-text-subdued);
}

.field-error {
  display: block;
  margin-top: var(--widget-spacing-small);
  font-size: 0.875rem;
  color: var(--p-color-text-critical);
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--widget-spacing-small);
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--widget-primary);
  color: var(--p-color-text-on-interactive);
  border-color: var(--widget-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--p-color-bg-interactive-hover);
  border-color: var(--p-color-bg-interactive-hover);
}

.btn-secondary {
  background: var(--p-color-surface);
  color: var(--p-color-text);
  border-color: var(--p-color-border);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--p-color-surface-hovered);
}

.btn-link {
  background: transparent;
  color: var(--p-color-bg-interactive);
  border: none;
  padding: var(--widget-spacing-small);
  font-weight: 400;
}

.btn-link:hover:not(:disabled) {
  color: var(--p-color-bg-interactive-hover);
  text-decoration: underline;
}

.btn-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.btn-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form actions */
.form-actions {
  display: flex;
  gap: var(--widget-spacing);
  align-items: center;
  justify-content: space-between;
  margin-top: var(--widget-spacing-large);
}

/* Order display styles */
.order-summary {
  padding: var(--widget-spacing-large);
  background: var(--p-color-surface-subdued);
  border-radius: var(--widget-border-radius);
  margin-bottom: var(--widget-spacing-large);
}

.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--widget-spacing);
}

.order-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.order-meta p {
  margin: var(--widget-spacing-small) 0;
  font-size: 0.875rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.status-fulfilled {
  background: var(--p-color-bg-success-subdued);
  color: var(--p-color-text-success);
}

.status-badge.status-partially-fulfilled {
  background: var(--p-color-bg-warning-subdued);
  color: var(--p-color-text-warning);
}

.status-badge.status-unfulfilled {
  background: var(--p-color-border-subdued);
  color: var(--p-color-text-subdued);
}

/* Order items */
.order-items {
  margin-bottom: var(--widget-spacing-large);
}

.order-items h3,
.order-items h4 {
  margin: 0 0 var(--widget-spacing) 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.order-item {
  display: flex;
  align-items: center;
  gap: var(--widget-spacing);
  padding: var(--widget-spacing);
  border: 1px solid var(--p-color-border-subdued);
  border-radius: var(--widget-border-radius);
  margin-bottom: var(--widget-spacing);
  background: var(--p-color-surface);
}

.item-image {
  flex-shrink: 0;
  border-radius: 4px;
  object-fit: cover;
}

.item-details {
  flex: 1;
}

.item-details h4,
.item-details h5 {
  margin: 0 0 var(--widget-spacing-small) 0;
  font-weight: 500;
  font-size: 1rem;
}

.item-details p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: var(--p-color-text-subdued);
}

.item-quantity,
.item-price {
  font-weight: 500;
  color: var(--p-color-text);
}

/* Order actions */
.order-actions {
  display: flex;
  gap: var(--widget-spacing);
  align-items: center;
  flex-wrap: wrap;
}

/* Return form styles */
.return-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--widget-spacing-large);
}

.return-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.back-btn {
  font-size: 0.875rem;
}

.returnable-items {
  margin-bottom: var(--widget-spacing-large);
}

.returnable-items h4 {
  margin: 0 0 var(--widget-spacing) 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.return-items-grid {
  display: grid;
  gap: var(--widget-spacing);
}

.return-item {
  border: 1px solid var(--p-color-border-subdued);
  border-radius: var(--widget-border-radius);
  background: var(--p-color-surface);
  transition: border-color 0.2s ease;
}

.return-item:has(.return-item-checkbox:checked) {
  border-color: var(--p-color-border-interactive);
  background: var(--p-color-bg-interactive-subdued, rgba(0, 107, 255, 0.05));
}

.return-item-header {
  padding: var(--widget-spacing);
  border-bottom: 1px solid var(--p-color-border-subdued);
}

.return-item-label {
  display: flex;
  align-items: center;
  gap: var(--widget-spacing-small);
  font-weight: 500;
  cursor: pointer;
  margin: 0;
}

.return-item-checkbox {
  margin: 0;
}

.checkbox-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--p-color-border);
  border-radius: 2px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.return-item-checkbox:checked + .checkbox-custom {
  background: var(--p-color-bg-interactive);
  border-color: var(--p-color-bg-interactive);
}

.return-item-checkbox:checked + .checkbox-custom::after {
  content: '✓';
  color: var(--p-color-text-on-interactive);
  font-size: 0.875rem;
  font-weight: bold;
}

.return-item-content {
  display: flex;
  align-items: center;
  gap: var(--widget-spacing);
  padding: var(--widget-spacing);
}

.return-item-image {
  flex-shrink: 0;
  border-radius: 4px;
  object-fit: cover;
}

.return-item-info {
  flex: 1;
}

.quantity-selector {
  margin-top: var(--widget-spacing-small);
}

.quantity-selector label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.return-quantity-select {
  padding: 0.5rem;
  border: 1px solid var(--p-color-border);
  border-radius: 4px;
  font-size: 0.875rem;
}

.return-details {
  margin-bottom: var(--widget-spacing-large);
}

.return-policy {
  margin-bottom: var(--widget-spacing-large);
  padding: var(--widget-spacing);
  background: var(--p-color-bg-warning-subdued);
  border-radius: var(--widget-border-radius);
  border: 1px solid var(--p-color-bg-warning);
}

.policy-notice {
  display: flex;
  gap: var(--widget-spacing-small);
  align-items: flex-start;
}

.info-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  color: var(--p-color-text-warning);
}

.policy-text p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--p-color-text-warning);
}

/* Tracking styles */
.tracking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--widget-spacing-large);
}

.tracking-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.tracking-overview {
  margin-bottom: var(--widget-spacing-large);
}

.tracking-overview h4 {
  margin: 0 0 var(--widget-spacing) 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.status-display {
  display: flex;
  align-items: center;
  gap: var(--widget-spacing);
}

.fulfillments-list {
  display: grid;
  gap: var(--widget-spacing);
}

.fulfillment-item {
  padding: var(--widget-spacing);
  border: 1px solid var(--p-color-border-subdued);
  border-radius: var(--widget-border-radius);
  background: var(--p-color-surface);
}

.fulfillment-item h5 {
  margin: 0 0 var(--widget-spacing-small) 0;
  font-weight: 500;
}

.fulfillment-item p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
}

.tracking-link {
  color: var(--p-color-bg-interactive);
  text-decoration: none;
  font-weight: 500;
}

.tracking-link:hover {
  text-decoration: underline;
}

.no-fulfillments {
  padding: var(--widget-spacing-large);
  text-align: center;
  color: var(--p-color-text-subdued);
}

.tracking-actions {
  margin-top: var(--widget-spacing-large);
}

/* Confirmation styles */
.confirmation-header {
  text-align: center;
  margin-bottom: var(--widget-spacing-large);
}

.success-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto var(--widget-spacing);
  color: var(--p-color-bg-success);
}

.confirmation-header h3 {
  margin: 0 0 var(--widget-spacing-small) 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--p-color-bg-success);
}

.confirmation-message {
  margin: 0;
  color: var(--p-color-text-subdued);
}

.return-summary {
  padding: var(--widget-spacing-large);
  background: var(--p-color-surface-subdued);
  border-radius: var(--widget-border-radius);
  margin-bottom: var(--widget-spacing-large);
}

.return-summary h4 {
  margin: 0 0 var(--widget-spacing) 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.return-info p {
  margin: var(--widget-spacing-small) 0;
  font-size: 0.875rem;
}

.return-items-summary {
  margin-bottom: var(--widget-spacing-large);
}

.return-items-summary h5 {
  margin: 0 0 var(--widget-spacing-small) 0;
  font-weight: 500;
}

.return-items-list {
  margin: 0;
  padding-left: var(--widget-spacing-large);
}

.return-items-list li {
  margin: 0.25rem 0;
  font-size: 0.875rem;
}

.next-steps {
  margin-bottom: var(--widget-spacing-large);
}

.next-steps h4 {
  margin: 0 0 var(--widget-spacing) 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.steps-list {
  display: grid;
  gap: var(--widget-spacing);
}

.step {
  display: flex;
  gap: var(--widget-spacing);
  align-items: flex-start;
}

.step-number {
  width: 2rem;
  height: 2rem;
  background: var(--p-color-bg-interactive);
  color: var(--p-color-text-on-interactive);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-content h5 {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
}

.step-content p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--p-color-text-subdued);
}

.confirmation-actions {
  display: flex;
  gap: var(--widget-spacing);
  justify-content: center;
  flex-wrap: wrap;
}

/* Error handling */
.error-container {
  margin-bottom: var(--widget-spacing);
}

.error-banner {
  display: flex;
  align-items: flex-start;
  gap: var(--widget-spacing-small);
  padding: var(--widget-spacing);
  background: var(--p-color-bg-critical-subdued);
  color: var(--p-color-text-critical);
  border: 1px solid var(--p-color-bg-critical);
  border-radius: 4px;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.error-banner p {
  margin: 0;
  flex: 1;
  font-size: 0.875rem;
}

.error-close {
  background: none;
  border: none;
  color: var(--p-color-text-critical);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Utility classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .real-mcoy-returns-widget {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
    padding: var(--widget-spacing);
  }
  
  .order-item {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }
  
  .return-item-content {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }
  
  .form-actions {
    flex-direction: column;
    gap: var(--widget-spacing-small);
  }
  
  .order-actions {
    flex-direction: column;
  }
  
  .confirmation-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .order-header {
    flex-direction: column;
    gap: var(--widget-spacing-small);
    align-items: flex-start;
  }
  
  .tracking-header {
    flex-direction: column;
    gap: var(--widget-spacing-small);
    align-items: flex-start;
  }
  
  .return-header {
    flex-direction: column;
    gap: var(--widget-spacing-small);
    align-items: flex-start;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .real-mcoy-returns-widget {
    --p-color-surface: #1a1a1a;
    --p-color-text: #ffffff;
    --p-color-text-subdued: #b3b3b3;
    --p-color-border: #404040;
    --p-color-border-subdued: #2a2a2a;
    --p-color-surface-subdued: #2a2a2a;
    --p-color-surface-hovered: #333333;
    --p-color-bg-interactive: #006eff;
    --p-color-text-on-interactive: #ffffff;
    --p-color-bg-interactive-hover: #0052cc;
    --p-color-border-interactive: #006eff;
    --p-color-bg-success: #008060;
    --p-color-bg-success-subdued: #003d32;
    --p-color-text-success: #00d9a7;
    --p-color-bg-critical: #d72c0d;
    --p-color-bg-critical-subdued: #4a0e04;
    --p-color-text-critical: #ff8a65;
    --p-color-bg-warning: #ffc453;
    --p-color-bg-warning-subdued: #4a3300;
    --p-color-text-warning: #ffcc02;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .real-mcoy-returns-widget {
    --p-color-border: #000000;
    --p-color-bg-interactive: #0000ff;
    --p-color-text-on-interactive: #ffffff;
  }
  
  .btn {
    border-width: 2px;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .widget-step,
  .btn,
  .form-input,
  .form-select,
  .form-textarea,
  .return-item {
    transition: none;
  }
  
  .spinner {
    animation: none;
  }
}