{"include": ["**/*.ts", "**/*.tsx", "**/.server/**/*.ts", "**/.server/**/*.tsx", "**/.client/**/*.ts", "**/.client/**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES6"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "bundler", "resolveJsonModule": true, "target": "ES2022", "strict": true, "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"]}, "noEmit": true}}