# Development Setup Guide
## Real McCoy Returns - Shopify Theme App Extension

### Prerequisites

#### System Requirements
- **Node.js:** v18.0.0 or higher
- **npm:** v8.0.0 or higher (or yarn/pnpm equivalent)
- **Git:** Latest version
- **Code Editor:** VS Code recommended with Shopify extensions

#### Required Accounts
- Shopify Partner Account (https://partners.shopify.com)
- Development Store: `returns-testing-store.myshopify.com`
- GitHub account (for version control)

#### Shopify CLI Installation
```bash
npm install -g @shopify/cli @shopify/theme
```

### Project Initialization

#### 1. Create Shopify App
```bash
# Navigate to your development directory
cd ~/Desktop

# Create new Shopify app
shopify app create real-mcoy-returns

# Select template: Remix
# Enter app name: Real McCoy Returns
# Select language: TypeScript (recommended)
```

#### 2. Configure App Settings

**Update `shopify.app.toml`:**
```toml
name = "real-mcoy-returns"
client_id = "YOUR_CLIENT_ID_FROM_PARTNER_DASHBOARD"
application_url = "https://YOUR_NGROK_URL"
embedded = true

[access_scopes]
scopes = "read_orders,write_orders,read_customers,write_customers,read_fulfillments,write_fulfillments,read_returns,write_returns"

[auth]
redirect_urls = [
  "https://YOUR_NGROK_URL/auth/callback",
  "https://YOUR_NGROK_URL/auth/shopify/callback"
]

[webhooks]
api_version = "2025-07"

[pos]
embedded = false
```

#### 3. Environment Configuration

**Create `.env` file:**
```env
# Shopify App Configuration
SHOPIFY_API_KEY=your_api_key_from_partner_dashboard
SHOPIFY_API_SECRET=your_api_secret_from_partner_dashboard
SCOPES=read_orders,write_orders,read_customers,write_customers,read_fulfillments,write_fulfillments,read_returns,write_returns
HOST=https://your-ngrok-url.ngrok.io

# Development Store
SHOPIFY_APP_URL=https://your-ngrok-url.ngrok.io
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret

# Session Storage
SESSION_SECRET=your_long_random_session_secret_key_here

# Development Settings
NODE_ENV=development
DEBUG_MODE=true
```

### MCP Server Setup

#### Shopify Dev MCP Server Configuration

**For Claude Desktop (`~/Library/Application Support/Claude/claude_desktop_config.json`):**
```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx",
      "args": ["-y", "@shopify/dev-mcp@latest"],
      "env": {
        "POLARIS_UNIFIED": "true"
      }
    }
  }
}
```

**For Cursor (`.cursor-settings/settings.json`):**
```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx", 
      "args": ["-y", "@shopify/dev-mcp@latest"],
      "env": {
        "POLARIS_UNIFIED": "true"
      }
    }
  }
}
```

### Theme App Extension Setup

#### 1. Generate Theme Extension
```bash
cd real-mcoy-returns
shopify app generate extension

# Select extension type: Theme app extension
# Enter extension name: real-mcoy-returns-widget
```

#### 2. Extension Configuration

**Update `extensions/real-mcoy-returns-widget/shopify.extension.toml`:**
```toml
name = "real-mcoy-returns-widget"
type = "theme_app_extension"

[extensions]
  [[extensions.blocks]]
    name = "Returns & Tracking Widget"
    target = "section"
    
  [[extensions.app_embeds]]
    name = "Returns Widget Embed"
    target = "head"

[extensions.settings]
  [[extensions.settings.groups]]
    name = "Widget Configuration"
    
    [[extensions.settings.groups.settings]]
      type = "text"
      id = "widget_title"
      label = "Widget Title"
      default = "Order Tracking & Returns"
      
    [[extensions.settings.groups.settings]]
      type = "color"
      id = "primary_color"
      label = "Primary Color"
      default = "#1a73e8"
      
    [[extensions.settings.groups.settings]]
      type = "checkbox"
      id = "enable_returns"
      label = "Enable Returns"
      default = true
      
    [[extensions.settings.groups.settings]]
      type = "checkbox"
      id = "enable_tracking"
      label = "Enable Order Tracking"
      default = true
```

### Development Store Configuration

#### 1. Install App in Development Store
```bash
# Start development server
shopify app dev

# Follow CLI prompts to:
# 1. Select your development store: returns-testing-store.myshopify.com
# 2. Install the app
# 3. Open preview URL
```

#### 2. Create Test Data

**Create test orders in development store:**
```bash
# Access development store admin
# Go to Orders > Create order
# Create several test orders with different statuses:
# - Order #1001: Fulfilled (for tracking testing)
# - Order #1002: Partially fulfilled (for tracking testing)
# - Order #1003: Unfulfilled (for return testing)
```

**Test order details to use:**
```
Order #1001:
- Email: <EMAIL>
- Status: Fulfilled
- Items: 2x T-Shirt, 1x Hoodie

Order #1002: 
- Email: <EMAIL>
- Status: Partially fulfilled
- Items: 1x Jeans (fulfilled), 1x Jacket (unfulfilled)

Order #1003:
- Email: <EMAIL>
- Status: Unfulfilled
- Items: 3x Sneakers
```

### File Structure Setup

#### Create Required Directories
```bash
cd real-mcoy-returns

# Create backend directories
mkdir -p app/lib
mkdir -p app/routes

# Create frontend directories  
mkdir -p extensions/real-mcoy-returns-widget/assets
mkdir -p extensions/real-mcoy-returns-widget/blocks
mkdir -p extensions/real-mcoy-returns-widget/snippets
mkdir -p extensions/real-mcoy-returns-widget/locales

# Create documentation directories
mkdir -p docs
mkdir -p tests/unit
mkdir -p tests/integration
```

#### Initialize Core Files

**Create placeholder files:**
```bash
# Backend files
touch app/lib/shopify-graphql.ts
touch app/lib/order-validation.ts
touch app/lib/return-logic.ts
touch app/routes/api.orders.lookup.ts
touch app/routes/api.returns.create.ts

# Frontend files
touch extensions/real-mcoy-returns-widget/blocks/returns-widget.liquid
touch extensions/real-mcoy-returns-widget/assets/widget.js
touch extensions/real-mcoy-returns-widget/assets/widget.css
touch extensions/real-mcoy-returns-widget/snippets/order-lookup.liquid
touch extensions/real-mcoy-returns-widget/snippets/order-display.liquid
touch extensions/real-mcoy-returns-widget/snippets/return-form.liquid
touch extensions/real-mcoy-returns-widget/locales/en.default.json

# Documentation files
touch docs/api-reference.md
touch docs/user-guide.md
touch README.md
```

### Development Workflow

#### 1. Local Development Commands
```bash
# Start development server (runs app + extension)
shopify app dev

# Deploy extension only
shopify app deploy

# Check extension for issues
shopify extension check

# Build for production
npm run build
```

#### 2. Git Setup
```bash
# Initialize git repository
git init
git add .
git commit -m "Initial project setup"

# Add remote repository
git remote add origin https://github.com/yourusername/real-mcoy-returns.git
git branch -M main
git push -u origin main
```

#### 3. Development Server URLs
When running `shopify app dev`, you'll get:
- **App URL:** https://your-ngrok-url.ngrok.io
- **Extension Preview:** https://returns-testing-store.myshopify.com/admin/themes/current/editor
- **GraphQL Playground:** https://your-ngrok-url.ngrok.io/graphiql

### VS Code Extensions

#### Recommended Extensions
```bash
# Install via VS Code extensions panel:
# - Shopify Liquid (Shopify.theme-check-vscode)
# - Shopify CLI (Shopify.shopify-vscode)
# - GraphQL (GraphQL.vscode-graphql)
# - Thunder Client (rangav.vscode-thunder-client) - for API testing
# - Auto Rename Tag (formulahendry.auto-rename-tag)
# - Prettier (esbenp.prettier-vscode)
# - ES7+ Snippets (dsznajder.es7-react-js-snippets)
```

#### VS Code Settings

**Create `.vscode/settings.json`:**
```json
{
  "shopify.cli.path": "/usr/local/bin/shopify",
  "liquid.format.enable": true,
  "liquid.completion.enable": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true
  },
  "files.associations": {
    "*.liquid": "liquid"
  }
}
```

**Create `.vscode/extensions.json`:**
```json
{
  "recommendations": [
    "shopify.theme-check-vscode",
    "shopify.shopify-vscode", 
    "graphql.vscode-graphql",
    "rangav.vscode-thunder-client",
    "esbenp.prettier-vscode"
  ]
}
```

### Testing Setup

#### 1. Install Testing Dependencies
```bash
npm install --save-dev jest @types/jest ts-jest
npm install --save-dev @testing-library/dom @testing-library/jest-dom
npm install --save-dev jsdom
```

#### 2. Jest Configuration

**Create `jest.config.js`:**
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  roots: ['<rootDir>/app', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.(ts|js)',
    '**/*.(test|spec).(ts|js)'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  collectCoverageFrom: [
    'app/**/*.(ts|js)',
    '!app/**/*.d.ts'
  ]
};
```

**Create `tests/setup.ts`:**
```typescript
import '@testing-library/jest-dom';

// Mock Shopify global objects
global.Shopify = {
  shop: 'returns-testing-store.myshopify.com',
  theme: { id: 123456789 }
};

// Mock fetch
global.fetch = jest.fn();
```

#### 3. Package.json Scripts

**Update `package.json`:**
```json
{
  "scripts": {
    "build": "remix build",
    "dev": "shopify app dev",
    "deploy": "shopify app deploy", 
    "start": "remix-serve build",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint app/ extensions/",
    "format": "prettier --write app/ extensions/"
  }
}
```

### Documentation Setup

#### 1. README.md Template
```markdown
# Real McCoy Returns - Shopify App

Customer-facing order tracking and returns management widget.

## Features
- Order lookup via email + order number
- Real-time tracking information
- Self-service return requests
- Return label generation

## Development Setup
1. Clone repository
2. Run `npm install`
3. Configure `.env` file
4. Run `shopify app dev`

## Testing
- `npm test` - Run all tests
- `npm run test:watch` - Watch mode
- `npm run test:coverage` - Coverage report

## Deployment
1. `npm run build` - Build for production
2. `shopify app deploy` - Deploy to Shopify

## Documentation
- [API Reference](docs/api-reference.md)
- [User Guide](docs/user-guide.md)
- [Contributing Guidelines](CONTRIBUTING.md)
```

#### 2. API Documentation Template

**Create `docs/api-reference.md`:**
```markdown
# API Reference

## Endpoints

### POST /api/orders/lookup
Lookup order by email and order number.

**Request:**
```json
{
  "email": "<EMAIL>",
  "orderNumber": "1001"
}
```

**Response:**
```json
{
  "success": true,
  "order": {
    "id": "gid://shopify/Order/123",
    "name": "#1001",
    "email": "<EMAIL>"
  }
}
```

### POST /api/returns/create
Create return request.

**Request:**
```json
{
  "orderId": "gid://shopify/Order/123",
  "items": [
    {
      "lineItemId": "gid://shopify/LineItem/456",
      "quantity": 1,
      "reason": "defective"
    }
  ],
  "comments": "Item arrived damaged"
}
```
```

### Troubleshooting

#### Common Issues

**1. "App not found" error:**
```bash
# Solution: Ensure app is properly installed
shopify app info
shopify app deploy
```

**2. GraphQL permission errors:**
```bash
# Solution: Check scopes in shopify.app.toml
# Reinstall app with updated scopes
```

**3. Extension not appearing in theme editor:**
```bash
# Solution: Enable development store preview
# In Partner Dashboard: Apps > Your App > Extensions > Enable Preview
```

**4. MCP server not connecting:**
```bash
# Solution: Check configuration paths
# Restart Claude Desktop/Cursor
# Verify npx command works: npx @shopify/dev-mcp@latest
```

### Next Steps

After completing this setup:

1. **Verify Installation:**
   - App runs without errors: `shopify app dev`
   - Extension appears in theme editor
   - Test orders exist in development store

2. **Begin Implementation:**
   - Start with order lookup functionality
   - Implement one feature at a time
   - Test thoroughly on mobile and desktop

3. **Use AI Coding Assistants:**
   - Reference the Context Engineering Guide
   - Use MCP server for Shopify documentation
   - Follow established patterns and conventions

4. **Maintain Quality:**
   - Write tests for all functionality
   - Follow accessibility guidelines
   - Keep documentation updated

You're now ready to start building the Real McCoy Returns app with AI coding assistance!