{"widget": {"title": "Order Tracking & Returns", "description": "Look up your order and request returns easily"}, "lookup": {"title": "Find Your Order", "description": "Enter your email address and order number to track your order or request a return.", "email_label": "Email Address", "email_placeholder": "Enter your email address", "email_help": "Enter the email address used when placing your order", "order_number_label": "Order Number", "order_number_placeholder": "e.g. #1001 or 1001", "order_number_help": "You can find your order number in your email confirmation", "submit_button": "Find My Order", "required": "required"}, "order": {"title": "Order Details", "order_date": "Order Date", "total": "Total", "status": "Status", "email": "Email", "items_title": "Items Ordered", "quantity": "Quantity", "price": "Price", "track_button": "Track Order", "return_button": "Request Return", "back_button": "Back to Search"}, "tracking": {"title": "Order Tracking", "current_status": "Current Status", "shipment_details": "Shipment Details", "carrier": "Carrier", "tracking_number": "Tracking Number", "track_package": "Track Package", "no_tracking": "No shipment information available yet.", "refresh_button": "Refresh Status", "back_button": "Back to Order"}, "return": {"title": "Request a Return", "select_items": "Select Items to Return", "return_reason": "Reason for Return", "reason_placeholder": "Select a reason", "reasons": {"defective": "Defective/Damaged", "wrong_size": "Wrong Size", "wrong_item": "Wrong Item Received", "not_as_described": "Not as Described", "changed_mind": "Changed Mind", "other": "Other"}, "comments_label": "Additional Comments", "comments_placeholder": "Please provide additional details about your return...", "comments_help": "Help us process your return faster by providing additional details", "quantity_label": "Quantity to return:", "policy_title": "Return Policy:", "policy_text": "Items must be returned within {days} days of delivery in original condition with tags attached.", "submit_button": "Submit Return Request", "back_button": "Back to Order", "optional": "(Optional)"}, "confirmation": {"title": "Return Request Submitted", "message": "Your return request has been successfully submitted and is being processed.", "return_summary": "Return Summary", "return_id": "Return ID", "status": "Status", "items": "Items", "items_being_returned": "Items Being Returned:", "next_steps": "What happens next?", "step_1_title": "Processing", "step_1_text": "We'll review your return request within 1-2 business days.", "step_2_title": "Return Label", "step_2_text": "Once approved, you'll receive a prepaid return shipping label via email.", "step_3_title": "Ship Items", "step_3_text": "Package your items securely and drop them off at any authorized shipping location.", "step_4_title": "Refund", "step_4_text": "Your refund will be processed within 5-7 business days after we receive your items.", "print_button": "Print Confirmation", "new_lookup_button": "Look Up Another Order"}, "errors": {"required_fields": "Email and order number are required", "invalid_email": "Please enter a valid email address", "invalid_order_number": "Please enter a valid order number", "order_not_found": "Order not found. Please check your email address and order number.", "network_error": "Unable to connect. Please check your connection and try again.", "server_error": "An unexpected error occurred. Please try again.", "no_items_selected": "Please select at least one item to return.", "no_return_reason": "Please select a reason for your return.", "return_failed": "Unable to create return request. Please try again.", "tracking_unavailable": "Order tracking is not available.", "returns_unavailable": "Returns are not available for this order."}, "loading": {"searching": "Searching for your order...", "submitting": "Submitting return request...", "refreshing": "Refreshing tracking information..."}, "accessibility": {"close": "Close", "loading": "Loading", "error": "Error", "success": "Success", "required_field": "This field is required", "optional_field": "This field is optional"}}