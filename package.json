{"name": "real-mcoy-returns", "version": "1.0.0", "description": "Shopify Returns and Order Tracking Widget", "private": true, "scripts": {"build": "remix build", "dev": "shopify app dev", "deploy": "shopify app deploy", "start": "remix-serve build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint app/ extensions/", "format": "prettier --write app/ extensions/", "typecheck": "tsc --noEmit"}, "dependencies": {"@remix-run/node": "^2.12.0", "@remix-run/react": "^2.12.0", "@remix-run/serve": "^2.12.0", "@shopify/shopify-api": "^11.0.0", "@shopify/shopify-app-remix": "^3.0.0", "@shopify/polaris": "^13.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "isbot": "^5.1.0"}, "devDependencies": {"@remix-run/dev": "^2.12.0", "@shopify/cli": "^3.83.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/jest": "^29.5.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-hydrogen": "^0.12.0", "eslint-plugin-jsx-a11y": "^6.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^25.0.0", "prettier": "^3.0.0", "ts-jest": "^29.2.0", "typescript": "^5.4.0", "vite": "^5.4.0"}, "engines": {"node": ">=20.0.0"}}