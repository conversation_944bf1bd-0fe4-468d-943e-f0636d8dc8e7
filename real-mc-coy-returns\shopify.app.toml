# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "092204d79cf2527823ec4062a7fe1a77"
name = "Real McCoy Returns"
handle = "real-mccoy-returns"
application_url = "https://example.com/"
embedded = true

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = [ "https://example.com/api/auth" ]

[pos]
embedded = false
