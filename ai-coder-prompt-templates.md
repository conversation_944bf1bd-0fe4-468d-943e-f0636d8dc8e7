# AI Coder Prompt Templates
## Real McCoy Returns - Shopify Theme App Extension

### Overview
This document contains optimized prompts for AI coding assistants (<PERSON>, GitHub Copilot, Cursor, etc.) to build the Real McCoy Returns app efficiently. Each prompt includes context, requirements, and expected deliverables.

---

## Prompt Template 1: Project Initialization

### Prompt:
```
I'm building a Shopify Theme App Extension called "Real McCoy Returns" that enables customers to track orders and request returns directly on the storefront. 

**Project Context:**
- Shopify CLI app with Remix backend
- Theme app extension for frontend widget
- GraphQL Admin API integration
- Polaris UI design system
- Development store: returns-testing-store.myshopify.com

**Requirements:**
1. Create the main extension configuration file
2. Set up basic project structure with all required directories
3. Create package.json with all necessary dependencies
4. Set up TypeScript configuration for the project

**Deliverables