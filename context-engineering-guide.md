# Context Engineering Guide for AI Coders
## Real McCoy Returns - Shopify Theme App Extension

### Overview
This guide provides AI coding assistants (Claude Code, GitHub Copilot, Cursor, etc.) with essential context for building the Real McCoy Returns Shopify app. Use this document to understand the project structure, patterns, and requirements before generating code.

### Project Context

#### What We're Building
**Real McCoy Returns** is a Shopify Theme App Extension that enables customers to:
1. Look up their orders using email + order number
2. Track order fulfillment and shipping status  
3. Request returns for eligible items
4. Generate return shipping labels
5. Navigate through the process seamlessly

#### Key Technical Stack
- **Platform:** Shopify Theme App Extension (storefront widget)
- **Backend:** Remix with GraphQL Admin API integration
- **Frontend:** Vanilla JavaScript + Liquid templates  
- **UI Framework:** Shopify Polaris design system
- **APIs:** Shopify GraphQL Admin API (orders, returns, fulfillments)
- **Development Store:** returns-testing-store.myshopify.com

### Required Shopify Knowledge

#### Theme App Extensions Fundamentals
Theme App Extensions allow apps to inject functionality into Shopify themes without requiring merchants to edit code. Key concepts:

- **App Blocks:** Sections merchants can add/remove via theme editor
- **App Embeds:** Always-present elements (like floating widgets)
- **Liquid Templates:** Server-side rendering for initial HTML
- **JavaScript Enhancement:** Client-side interactivity and API calls
- **Polaris Styling:** Consistent with Shopify's design system

#### Essential Shopify GraphQL Patterns

**Order Lookup Pattern:**
```graphql
query orderLookup($query: String!) {
  orders(first: 1, query: $query) {
    edges {
      node {
        id
        name
        email
        createdAt
        displayFulfillmentStatus
        totalPriceSet {
          presentmentMoney {
            amount
            currencyCode
          }
        }
        lineItems(first: 50) {
          edges {
            node {
              id
              title
              quantity
              variant {
                id
                image { url }
              }
            }
          }
        }
      }
    }
  }
}

# Search query format: "name:1001 AND email:<EMAIL>"
```

**Return Creation Pattern:**
```graphql
mutation returnCreate($input: ReturnInput!) {
  returnCreate(input: $input) {
    return {
      id
      status
      returnLineItems(first: 50) {
        edges {
          node {
            id
            quantity
            returnReason
          }
        }
      }
    }
    userErrors {
      field
      message
    }
  }
}
```

#### Required API Scopes
```
read_orders, write_orders
read_customers, write_customers
read_fulfillments, write_fulfillments  
read_returns, write_returns
```

### Architecture Patterns

#### File Organization Pattern
```
real-mcoy-returns/
├── app/                          # Remix backend
│   ├── routes/api.*.ts          # API endpoints
│   ├── lib/shopify-graphql.ts   # GraphQL utilities
│   └── shopify.server.ts        # Shopify auth
├── extensions/
│   └── real-mcoy-returns-widget/
│       ├── blocks/*.liquid      # Main widget blocks
│       ├── snippets/*.liquid    # Reusable components
│       ├── assets/*.{js,css}    # Client-side code
│       └── shopify.extension.toml
```

#### API Route Pattern
All API routes should follow this structure:
```typescript
// app/routes/api.{resource}.{action}.ts
import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export async function action({ request }: ActionFunctionArgs) {
  const { admin } = await authenticate.public.appProxy(request);
  
  // Validate HTTP method
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    // Validate input
    // Call GraphQL
    // Return standardized response
    return json({ success: true, data: result });
  } catch (error) {
    console.error("API Error:", error);
    return json({ success: false, error: "Internal server error" }, { status: 500 });
  }
}
```

#### Widget JavaScript Pattern
The main widget should be a class-based component:
```javascript
class RealMcCoyReturnsWidget {
  constructor(element) {
    this.element = element;
    this.currentStep = 'order-lookup-form';
    this.orderData = null;
    this.init();
  }

  init() {
    this.bindEvents();
    this.setupFormValidation();
  }

  async makeAPICall(endpoint, data) {
    // Standardized API call pattern
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      this.showError('Network error. Please try again.');
      return { success: false };
    }
  }

  showStep(stepId) {
    // Hide all steps, show target step
  }

  showLoading(show) {
    // Toggle loading spinner
  }

  showError(message) {
    // Display error banner
  }
}
```

### Polaris UI Integration

#### Essential Polaris Patterns for Liquid
```liquid
<!-- Form Layout -->
<div class="form-group">
  <label for="input-id">Label Text</label>
  <input 
    type="text" 
    id="input-id" 
    name="field_name" 
    class="form-input"
    required
  >
  <small class="help-text">Helper text</small>
</div>

<!-- Button Group -->
<div class="form-actions">
  <button type="button" class="btn btn-secondary">Secondary</button>
  <button type="submit" class="btn btn-primary">Primary</button>
</div>

<!-- Status Badge -->
<span class="status-badge status-{{ order.status | downcase }}">
  {{ order.displayFulfillmentStatus }}
</span>

<!-- Card Layout -->
<div class="order-summary">
  <h3>Order {{ order.name }}</h3>
  <p>Total: {{ order.totalPriceSet.presentmentMoney.amount }} {{ order.totalPriceSet.presentmentMoney.currencyCode }}</p>
</div>
```

#### CSS Custom Properties (Polaris Design Tokens)
```css
.real-mcoy-returns-widget {
  --p-color-surface: #ffffff;
  --p-color-text: #202223;
  --p-color-border: #c9cccf;
  --p-color-bg-interactive: #006eff;
  --p-color-text-on-interactive: #ffffff;
  /* Use these consistently throughout */
}
```

### Data Flow Patterns

#### Order Lookup Flow
1. **User Input:** Email + Order Number
2. **Client Validation:** Format checking, required fields
3. **API Call:** POST to `/api/orders/lookup`
4. **Server Processing:** GraphQL query with email verification
5. **Response Handling:** Success → show order details, Error → show message
6. **State Management:** Store order data for subsequent operations

#### Return Creation Flow
1. **Item Selection:** User selects returnable items
2. **Reason Collection:** Dropdown + optional comments
3. **Validation:** Ensure at least one item selected
4. **API Call:** POST to `/api/returns/create`
5. **GraphQL Mutation:** `returnCreate` with line items
6. **Label Generation:** Create return shipping label
7. **Confirmation:** Show return details and next steps

### Error Handling Patterns

#### API Error Response Standard
```json
{
  "success": false,
  "error": "User-friendly error message",
  "code": "ERROR_CODE", // Optional
  "details": {} // Optional debug data
}
```

#### Client-Side Error Display
```javascript
showError(message) {
  const errorContainer = document.createElement('div');
  errorContainer.className = 'error-banner';
  errorContainer.innerHTML = `
    <p>${message}</p>
    <button type="button" onclick="this.parentElement.remove()">×</button>
  `;
  this.element.insertBefore(errorContainer, this.element.firstChild);
}
```

### Security Patterns

#### Input Validation
- **Email:** Use regex validation + server-side verification
- **Order Numbers:** Strip `#` prefix, validate format
- **Return Data:** Validate line item IDs exist in order
- **Quantity Limits:** Ensure quantities don't exceed available

#### API Security
- **Authentication:** Use Shopify's app proxy authentication
- **Email Verification:** Only show orders matching provided email
- **Rate Limiting:** Implement reasonable request limits
- **Error Messages:** Don't leak sensitive information

### Testing Patterns

#### Widget Testing Strategy
```javascript
// Test user interactions
describe('Order Lookup', () => {
  test('validates email format', () => {
    // Test email validation
  });
  
  test('handles successful order lookup', async () => {
    // Mock API response, test UI updates
  });
  
  test('displays error for invalid order', async () => {
    // Test error handling
  });
});
```

#### API Testing Strategy
```typescript
// Test API endpoints
describe('Order Lookup API', () => {
  test('returns order for valid email/number', async () => {
    // Mock GraphQL response
    // Test successful lookup
  });
  
  test('rejects invalid email format', async () => {
    // Test input validation
  });
});
```

### Performance Optimization Patterns

#### Lazy Loading Strategy
```javascript
// Load components on demand
async loadReturnForm() {
  if (!this.returnFormLoaded) {
    const { ReturnFormHandler } = await import('./components/return-form.js');
    this.returnFormHandler = new ReturnFormHandler(this.element);
    this.returnFormLoaded = true;
  }
}
```

#### API Caching Pattern
```javascript
class APICache {
  constructor(ttl = 300000) { // 5 minutes
    this.cache = new Map();
    this.ttl = ttl;
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
}
```

### Common Implementation Patterns

#### Multi-Step Form Navigation
```javascript
const STEPS = {
  LOOKUP: 'order-lookup-form',
  DISPLAY: 'order-display', 
  TRACKING: 'tracking-info',
  RETURN: 'return-form',
  LABEL: 'return-label'
};

navigateToStep(step) {
  // Validate step transition
  if (!this.canNavigateTo(step)) return;
  
  // Update state
  this.previousStep = this.currentStep;
  this.currentStep = step;
  
  // Update UI
  this.showStep(step);
  
  // Update URL hash for bookmarking
  window.location.hash = step;
}
```

#### Form State Persistence
```javascript
saveFormState() {
  const formData = {
    step: this.currentStep,
    orderData: this.orderData,
    returnItems: this.returnItems,
    timestamp: Date.now()
  };
  
  sessionStorage.setItem('mcoy-returns-state', JSON.stringify(formData));
}

restoreFormState() {
  const saved = sessionStorage.getItem('mcoy-returns-state');
  if (!saved) return false;
  
  const state = JSON.parse(saved);
  
  // Check if state is fresh (< 30 minutes)
  if (Date.now() - state.timestamp > 1800000) {
    sessionStorage.removeItem('mcoy-returns-state');
    return false;
  }
  
  this.orderData = state.orderData;
  this.returnItems = state.returnItems;
  this.navigateToStep(state.step);
  return true;
}
```

### Accessibility Implementation

#### ARIA Labels and Roles
```liquid
<div class="widget-step" 
     role="tabpanel" 
     aria-labelledby="step-{{ step_id }}-label"
     aria-hidden="{{ step_active | unless: 'true' }}">
  
  <h3 id="step-{{ step_id }}-label">{{ step_title }}</h3>
  
  <form role="form" aria-label="Order lookup form">
    <div class="form-group">
      <label for="email" id="email-label">
        Email Address
        <span aria-label="required" class="required">*</span>
      </label>
      <input 
        type="email" 
        id="email"
        name="email"
        aria-describedby="email-help email-error"
        aria-required="true"
        aria-invalid="false"
      >
      <div id="email-help" class="help-text">
        Enter the email address used for your order
      </div>
      <div id="email-error" class="error-text" aria-live="polite">
        <!-- Error messages appear here -->
      </div>
    </div>
  </form>
</div>
```

#### Keyboard Navigation
```javascript
setupKeyboardNavigation() {
  this.element.addEventListener('keydown', (e) => {
    // Escape key closes modals/goes back
    if (e.key === 'Escape') {
      this.handleEscape();
    }
    
    // Enter key on buttons
    if (e.key === 'Enter' && e.target.matches('button[type="button"]')) {
      e.target.click();
    }
    
    // Arrow keys for step navigation (when appropriate)
    if (e.key === 'ArrowLeft' && e.ctrlKey) {
      this.navigateBack();
    }
    if (e.key === 'ArrowRight' && e.ctrlKey) {
      this.navigateForward();
    }
  });
}
```

### Responsive Design Guidelines

#### Mobile-First CSS Pattern
```css
/* Mobile styles (default) */
.real-mcoy-returns-widget {
  padding: 1rem;
  margin: 0;
}

.order-item {
  flex-direction: column;
  text-align: center;
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Tablet and up */
@media (min-width: 768px) {
  .real-mcoy-returns-widget {
    padding: 1.5rem;
    margin: 0 auto;
    max-width: 600px;
  }
  
  .order-item {
    flex-direction: row;
    text-align: left;
  }
  
  .form-actions {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .real-mcoy-returns-widget {
    max-width: 800px;
  }
}
```

#### Touch-Friendly Interactions
```css
/* Ensure minimum 44px touch targets */
.btn, .form-input, .form-select {
  min-height: 44px;
}

/* Larger tap areas for mobile */
@media (max-width: 767px) {
  .btn {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
  }
  
  .clickable-item {
    padding: 1rem;
    margin: 0.5rem 0;
  }
}
```

### Integration Patterns

#### Shopify Admin API Client Setup
```typescript
// app/lib/shopify-graphql.ts
import { GraphqlQueryError } from "@shopify/shopify-api";

export class ShopifyGraphQLClient {
  constructor(private graphql: any) {}
  
  async query<T>(query: string, variables?: any): Promise<T> {
    try {
      const response = await this.graphql(query, { variables });
      
      if (response.data) {
        return response.data;
      }
      
      throw new Error('No data in GraphQL response');
    } catch (error) {
      if (error instanceof GraphqlQueryError) {
        console.error('GraphQL Error:', error.message);
        throw new Error('GraphQL query failed');
      }
      throw error;
    }
  }
  
  async mutate<T>(mutation: string, variables: any): Promise<T> {
    return this.query<T>(mutation, variables);
  }
}
```

#### Environment-Specific Configuration
```typescript
// app/lib/config.ts
export const CONFIG = {
  API_VERSION: '2025-07',
  RETURN_WINDOW_DAYS: parseInt(process.env.RETURN_WINDOW_DAYS || '30'),
  MAX_RETURN_ITEMS: parseInt(process.env.MAX_RETURN_ITEMS || '10'),
  ENABLE_TRACKING: process.env.ENABLE_TRACKING === 'true',
  DEBUG_MODE: process.env.NODE_ENV === 'development'
};

export const ENDPOINTS = {
  ORDER_LOOKUP: '/api/orders/lookup',
  RETURN_CREATE: '/api/returns/create',
  RETURN_LABEL: '/api/returns/{id}/label'
};
```

### Debugging Patterns

#### Development Console Logging
```javascript
class Logger {
  constructor(prefix = 'RealMcCoyReturns') {
    this.prefix = prefix;
    this.debugMode = document.querySelector('html').hasAttribute('data-debug');
  }
  
  debug(message, data = null) {
    if (this.debugMode) {
      console.log(`[${this.prefix}] ${message}`, data || '');
    }
  }
  
  error(message, error = null) {
    console.error(`[${this.prefix}] ERROR: ${message}`, error || '');
  }
  
  warn(message, data = null) {
    console.warn(`[${this.prefix}] WARNING: ${message}`, data || '');
  }
}

const logger = new Logger();
```

#### API Request/Response Logging
```typescript
export async function loggedGraphQLCall(
  graphql: any, 
  query: string, 
  variables: any,
  operationName: string
) {
  const startTime = Date.now();
  
  try {
    console.log(`[GraphQL] Starting ${operationName}`, { variables });
    
    const result = await graphql(query, { variables });
    
    const duration = Date.now() - startTime;
    console.log(`[GraphQL] Completed ${operationName} in ${duration}ms`);
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[GraphQL] Failed ${operationName} after ${duration}ms`, error);
    throw error;
  }
}
```

### Shopify-Specific Best Practices

#### Handling Shopify's Rate Limits
```typescript
class RateLimitedClient {
  constructor(private client: ShopifyGraphQLClient) {
    this.requestQueue = [];
    this.processing = false;
  }
  
  async makeRequest(query: string, variables?: any) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ query, variables, resolve, reject });
      this.processQueue();
    });
  }
  
  private async processQueue() {
    if (this.processing || this.requestQueue.length === 0) return;
    
    this.processing = true;
    
    while (this.requestQueue.length > 0) {
      const { query, variables, resolve, reject } = this.requestQueue.shift();
      
      try {
        const result = await this.client.query(query, variables);
        resolve(result);
        
        // Small delay to respect rate limits
        await new Promise(r => setTimeout(r, 100));
      } catch (error) {
        if (error.message.includes('rate limit')) {
          // Re-queue the request
          this.requestQueue.unshift({ query, variables, resolve, reject });
          // Wait longer before retry
          await new Promise(r => setTimeout(r, 2000));
        } else {
          reject(error);
        }
      }
    }
    
    this.processing = false;
  }
}
```

#### Shopify ID Format Handling
```typescript
export function normalizeShopifyId(id: string): string {
  // Convert legacy numeric IDs to GraphQL format
  if (!id.startsWith('gid://')) {
    return `gid://shopify/Order/${id}`;
  }
  return id;
}

export function extractNumericId(gid: string): string {
  // Extract numeric ID from GraphQL ID
  return gid.split('/').pop() || '';
}
```

### Theme Integration Considerations

#### CSS Isolation Pattern
```css
/* Prefix all widget styles to avoid conflicts */
.real-mcoy-returns-widget {
  /* Reset common properties that might be inherited */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  color: #202223;
  
  /* Isolate from theme styles */
  all: initial;
  font-family: inherit;
  
  /* Then apply our styles */
  display: block;
  max-width: 600px;
  margin: 2rem auto;
  padding: 1.5rem;
}

/* Ensure all child elements are also isolated */
.real-mcoy-returns-widget * {
  box-sizing: border-box;
}
```

#### Theme Color Integration
```liquid
{%- liquid
  # Try to inherit theme colors if available
  assign primary_color = block.settings.primary_color | default: settings.color_primary | default: '#006eff'
  assign text_color = block.settings.text_color | default: settings.color_text | default: '#202223'
  assign background_color = block.settings.background_color | default: settings.color_background | default: '#ffffff'
-%}

<style>
  .real-mcoy-returns-widget {
    --widget-primary: {{ primary_color }};
    --widget-text: {{ text_color }};
    --widget-background: {{ background_color }};
  }
</style>
```

### Final Implementation Notes

#### Code Generation Priorities
When generating code, prioritize in this order:
1. **Security** - Validate all inputs, authenticate all requests
2. **Accessibility** - Include proper ARIA labels and keyboard navigation
3. **Performance** - Use lazy loading and caching where appropriate
4. **User Experience** - Clear error messages and loading states
5. **Maintainability** - Follow established patterns and conventions

#### Common Gotchas to Avoid
- **Shopify ID Formats:** Always use GraphQL IDs (gid://shopify/...)
- **Email Matching:** Case-insensitive comparison required
- **Order Number Formats:** Handle with/without # prefix
- **Rate Limits:** Implement proper delays and retry logic
- **Browser Compatibility:** Test in Safari, Chrome, Firefox, Edge
- **Mobile Performance:** Optimize for slower networks and devices

#### Success Criteria
The implementation should achieve:
- ✅ Orders lookup working with email + order number
- ✅ Order details display with items, status, tracking
- ✅ Return request form with item selection
- ✅ Return submission creates Shopify return record
- ✅ Return label generation (basic implementation)
- ✅ Mobile-responsive design matching Polaris patterns
- ✅ Error handling for all failure scenarios
- ✅ Loading states for all async operations
- ✅ Accessibility compliance (WCAG 2.1 AA)

This context guide provides AI coders with the essential patterns, conventions, and requirements needed to successfully implement the Real McCoy Returns Shopify app extension.