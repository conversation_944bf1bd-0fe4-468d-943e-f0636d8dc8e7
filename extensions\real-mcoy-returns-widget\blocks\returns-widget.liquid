{%- liquid
  # Configuration from theme editor
  assign widget_title = block.settings.widget_title | default: 'Order Tracking & Returns'
  assign primary_color = block.settings.primary_color | default: '#1a73e8'
  assign enable_returns = block.settings.enable_returns | default: true
  assign enable_tracking = block.settings.enable_tracking | default: true
  assign return_window_days = block.settings.return_window_days | default: 30
-%}

<div class="real-mcoy-returns-widget" 
     data-primary-color="{{ primary_color }}"
     data-enable-returns="{{ enable_returns }}"
     data-enable-tracking="{{ enable_tracking }}"
     data-return-window="{{ return_window_days }}">
  
  <div class="widget-header">
    <h2>{{ widget_title }}</h2>
  </div>

  <div class="widget-content">
    <!-- Order Lookup Form -->
    <div id="order-lookup-form" class="widget-step active">
      {% render 'order-lookup' %}
    </div>

    <!-- Order Display -->
    <div id="order-display" class="widget-step hidden">
      {% render 'order-display' %}
    </div>

    <!-- Tracking Information -->
    <div id="tracking-info" class="widget-step hidden">
      {% render 'tracking-info' %}
    </div>

    <!-- Return Form -->
    <div id="return-form" class="widget-step hidden">
      {% render 'return-form' %}
    </div>

    <!-- Return Confirmation -->
    <div id="return-confirmation" class="widget-step hidden">
      {% render 'return-confirmation' %}
    </div>
  </div>
</div>

<script src="{{ 'widget.js' | asset_url }}" defer></script>
<link rel="stylesheet" href="{{ 'widget.css' | asset_url }}">

{% schema %}
{
  "name": "Returns & Tracking Widget",
  "target": "section",
  "settings": [
    {
      "type": "text",
      "id": "widget_title",
      "label": "Widget Title",
      "default": "Order Tracking & Returns"
    },
    {
      "type": "color",
      "id": "primary_color", 
      "label": "Primary Color",
      "default": "#1a73e8"
    },
    {
      "type": "checkbox",
      "id": "enable_returns",
      "label": "Enable Returns",
      "default": true
    },
    {
      "type": "checkbox", 
      "id": "enable_tracking",
      "label": "Enable Order Tracking",
      "default": true
    },
    {
      "type": "range",
      "id": "return_window_days",
      "label": "Return Window (Days)",
      "min": 7,
      "max": 90,
      "step": 1,
      "default": 30
    }
  ]
}
{% endschema %}