# Deployment Guide
## Real McCoy Returns - Shopify Theme App Extension

### Pre-Deployment Checklist

#### 1. Environment Setup
- [ ] Node.js 20.0.0+ installed
- [ ] Shopify CLI 3.83.0+ installed
- [ ] Shopify Partner account created
- [ ] Development store configured
- [ ] Environment variables configured in `.env`

#### 2. Code Quality
- [ ] All unit tests passing: `npm test`
- [ ] TypeScript compilation successful: `npm run typecheck`
- [ ] Code linting passed: `npm run lint`
- [ ] Code formatting applied: `npm run format`

#### 3. Functionality Testing
- [ ] Order lookup working with valid email/order number
- [ ] Order display showing correct information
- [ ] Return form functional with item selection
- [ ] Return submission creating Shopify returns
- [ ] Error handling working for all scenarios
- [ ] Mobile responsiveness verified

#### 4. Accessibility Testing
- [ ] Keyboard navigation working
- [ ] Screen reader compatibility tested
- [ ] Color contrast meets WCAG 2.1 AA standards
- [ ] Form labels and ARIA attributes present

### Deployment Steps

#### 1. Initial Setup

```bash
# Navigate to project directory
cd real-mcoy-returns

# Install dependencies
npm install

# Create environment file
cp .env.example .env
# Edit .env with your actual values
```

#### 2. Configure Shopify App

1. **Partner Dashboard Setup:**
   - Go to https://partners.shopify.com
   - Create new app or use existing
   - Note the API key and API secret
   - Set up OAuth redirect URLs

2. **Update Configuration:**
   ```bash
   # Edit shopify.app.toml
   vim shopify.app.toml
   
   # Update with your app details:
   # - client_id (API key)
   # - application_url
   # - redirect_urls
   ```

3. **Environment Variables:**
   ```env
   SHOPIFY_API_KEY=your_api_key_here
   SHOPIFY_API_SECRET=your_api_secret_here
   SCOPES=read_orders,write_orders,read_customers,write_customers,read_fulfillments,write_fulfillments,read_returns,write_returns
   HOST=https://your-app-url.com
   SESSION_SECRET=your_random_secret_key
   ```

#### 3. Development Deployment

```bash
# Start development server
npm run dev

# This will:
# 1. Start the Remix app
# 2. Start ngrok tunnel
# 3. Deploy theme extension to development store
# 4. Open app in browser
```

#### 4. Production Deployment

```bash
# Build for production
npm run build

# Deploy to Shopify
npm run deploy

# This will:
# 1. Build the Remix app
# 2. Deploy theme extension to production
# 3. Make app available in App Store (if configured)
```

### Post-Deployment Verification

#### 1. App Installation
- [ ] App installs successfully in test store
- [ ] Required permissions granted
- [ ] No installation errors in logs

#### 2. Theme Extension
- [ ] Widget appears in theme editor
- [ ] Block can be added to sections
- [ ] Settings panel shows all options
- [ ] Widget renders on storefront

#### 3. Functionality Testing
- [ ] Order lookup with valid order works
- [ ] Order lookup with invalid order shows error
- [ ] Return submission creates return in Shopify admin
- [ ] All user interactions work as expected

#### 4. Performance Testing
- [ ] Widget loads in <2 seconds
- [ ] API responses in <3 seconds
- [ ] Mobile performance acceptable
- [ ] No console errors

### Monitoring & Maintenance

#### 1. Error Monitoring
Set up monitoring for:
- API endpoint failures
- JavaScript errors
- Performance issues
- User experience problems

#### 2. Analytics
Track:
- Widget usage rates
- Successful order lookups
- Return request completions
- User drop-off points

#### 3. Regular Updates
- Monitor Shopify API changes
- Update dependencies regularly
- Test with new browser versions
- Review and update documentation

### Troubleshooting

#### Common Issues

**1. "App not found" error:**
```bash
# Solution: Redeploy the app
shopify app deploy
```

**2. GraphQL permission errors:**
```bash
# Check scopes in shopify.app.toml
# Reinstall app with updated scopes
```

**3. Extension not appearing:**
- Check extension is deployed: `shopify app info`
- Verify theme compatibility
- Clear browser cache

**4. API endpoints returning 500:**
- Check environment variables
- Verify database connection
- Check Shopify API credentials

**5. Widget not loading:**
- Check asset URLs in browser network tab
- Verify JavaScript console for errors
- Test theme compatibility

#### Debug Mode

Enable debug mode for detailed logging:

```env
NODE_ENV=development
DEBUG_MODE=true
```

This will:
- Show detailed API logs
- Enable JavaScript console logging
- Display error details to developers

### Security Considerations

#### 1. API Security
- [ ] All API endpoints validate input
- [ ] Email verification prevents data leaks
- [ ] Rate limiting implemented
- [ ] HTTPS enforced

#### 2. Data Privacy
- [ ] No sensitive data stored in browser
- [ ] Session data encrypted
- [ ] GDPR compliance maintained
- [ ] Data retention policies followed

#### 3. Access Control
- [ ] App proxy authentication working
- [ ] Order access restricted to order email
- [ ] Return creation properly validated
- [ ] No unauthorized data access possible

### Support & Documentation

#### For Merchants
- Widget setup guide
- Customization options
- Troubleshooting common issues
- Contact information

#### For Developers
- API documentation
- Code examples
- Integration guides
- Change logs

#### Support Channels
- GitHub Issues
- Support email
- Shopify Partner Discord
- Documentation wiki

### Success Metrics

Track these KPIs post-deployment:
- Customer service ticket reduction (target: 40%)
- User adoption rate (target: 60%)
- Task completion rate (target: 85%)
- Customer satisfaction (target: 4.5+ stars)
- Performance (target: <3s response time)
- Error rate (target: <5%)

This deployment guide ensures a smooth, secure, and successful launch of the Real McCoy Returns app.